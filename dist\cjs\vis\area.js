var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/area.ts
var area_exports = {};
__export(area_exports, {
  Area: () => Area
});
module.exports = __toCommonJS(area_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Area(options) {
  const {
    data,
    title,
    width = 600,
    height = 400,
    stack,
    axisYTitle,
    axisXTitle,
    theme = "default"
  } = options;
  let encode = {};
  let transform = [];
  let children = [];
  if (stack) {
    encode = { x: "time", y: "value", color: "group" };
    transform = [{ type: "stackY" }];
    children = [
      {
        type: "area",
        style: { fillOpacity: 0.6 }
      },
      {
        type: "line",
        style: { lineWidth: 2, strokeOpacity: 0.6 }
      }
    ];
  } else {
    encode = { x: "time", y: "value" };
    children = [
      {
        type: "area",
        style: {
          fillOpacity: 0.6,
          ...theme === "academy" ? {} : { fill: "linear-gradient(-90deg, white 0%, #3A95FF 100%)" }
        }
      },
      {
        type: "line",
        style: { lineWidth: 2, strokeOpacity: 0.6 }
      },
      {
        type: "point",
        encode: { shape: "point" },
        style: { fill: "white", lineWidth: 1 }
      }
    ];
  }
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "view",
    theme: import_theme.THEME_MAP[theme],
    title,
    data,
    width,
    height,
    encode,
    transform,
    insetRight: 12,
    style: { minHeight: 1 },
    axis: {
      y: {
        title: axisYTitle || false
      },
      x: {
        title: axisXTitle || false
      }
    },
    children
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Area
});
