var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/util/index.ts
var util_exports = {};
__export(util_exports, {
  ACADEMY_COLOR_PALETTE: () => import_palette.ACADEMY_COLOR_PALETTE,
  AssignColorByBranchTransform: () => import_assign_color_by_branch_transform.AssignColorByBranchTransform,
  DEFAULT_COLOR_PALETTE: () => import_palette.DEFAULT_COLOR_PALETTE,
  MindmapNode: () => import_mindmap_node.MindmapNode,
  OrganizationChartNode: () => import_organization_chart_node.OrganizationChartNode,
  groupBy: () => import_group_by.groupBy,
  treeToGraphData: () => import_graph_data.treeToGraphData
});
module.exports = __toCommonJS(util_exports);
var import_assign_color_by_branch_transform = require("./assign-color-by-branch-transform");
var import_graph_data = require("./graph-data");
var import_group_by = require("./group-by");
var import_mindmap_node = require("./mindmap-node");
var import_organization_chart_node = require("./organization-chart-node");
var import_palette = require("./palette");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ACADEMY_COLOR_PALETTE,
  AssignColorByBranchTransform,
  DEFAULT_COLOR_PALETTE,
  MindmapNode,
  OrganizationChartNode,
  groupBy,
  treeToGraphData
});
