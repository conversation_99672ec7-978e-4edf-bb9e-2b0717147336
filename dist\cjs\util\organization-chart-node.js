var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/util/organization-chart-node.ts
var organization_chart_node_exports = {};
__export(organization_chart_node_exports, {
  OrganizationChartNode: () => OrganizationChartNode
});
module.exports = __toCommonJS(organization_chart_node_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var { Rect, Label, Badge } = import_g6_ssr.G6;
var OrganizationChartNode = class extends Rect {
  get data() {
    return this.context.model.getElementDataById(this.id).data;
  }
  getLabelStyle() {
    const text = this.data.name;
    return {
      text,
      fill: "#2078B4",
      fontSize: 16,
      fontWeight: 400,
      textAlign: "left",
      transform: [["translate", -55, -10]]
    };
  }
  getKeyStyle(attributes) {
    return {
      ...super.getKeyStyle(attributes),
      fill: "#fff"
    };
  }
  // Draws the description shape.
  drawDescriptionShape(attributes, container) {
    const positionStyle = {
      text: this.data.description || "-",
      // Default text if no description is provided
      fontSize: 14,
      fill: "#343f4a",
      textAlign: "left",
      fontStyle: "italic",
      transform: [["translate", -55, 10]],
      wordWrapWidth: 200 - 32 - 16
      // Width of the node (200) minus the width of the status icon (32) and some padding (16)
    };
    this.upsert("position", Label, positionStyle, container);
  }
  // Draws the organization icon shape.
  drawOrganizationIconShape(attributes, container) {
    var _a;
    const { fill = "#1783FF" } = attributes;
    const iconStyle = {
      text: (((_a = this.data) == null ? void 0 : _a.name) || "V").slice(0, 1),
      fontSize: 18,
      textAlign: "center",
      // Half of the width of the node (200) minus half of the status icon width (32) plus some padding (4)
      transform: [["translate", -200 / 2 + 32 / 2 + 8, 0]],
      fill: "#000",
      backgroundFill: fill,
      backgroundRadius: 4,
      backgroundWidth: 32,
      backgroundHeight: 32,
      backgroundOpacity: 0.5
    };
    this.upsert("organization-icon", Badge, iconStyle, container);
  }
  render(attributes = this.parsedAttributes, container = this) {
    super.render(attributes, container);
    this.drawDescriptionShape(attributes, container);
    this.drawOrganizationIconShape(attributes, container);
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  OrganizationChartNode
});
