var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/util/assign-color-by-branch-transform.ts
var assign_color_by_branch_transform_exports = {};
__export(assign_color_by_branch_transform_exports, {
  AssignColorByBranchTransform: () => AssignColorByBranchTransform
});
module.exports = __toCommonJS(assign_color_by_branch_transform_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var import_palette = require("./palette");
var { BaseTransform } = import_g6_ssr.G6;
var _AssignColorByBranchTransform = class extends BaseTransform {
  constructor(context, options) {
    super(context, Object.assign({}, _AssignColorByBranchTransform.defaultOptions, options));
  }
  beforeDraw(input) {
    const nodes = this.context.model.getNodeData();
    if (nodes.length === 0)
      return input;
    let colorIndex = 0;
    const dfs = (nodeId, color) => {
      var _a;
      const node = nodes.find((datum) => datum.id == nodeId);
      if (!node)
        return;
      node.style || (node.style = {});
      node.style.color = color || this.options.colors[colorIndex++ % this.options.colors.length];
      (_a = node.children) == null ? void 0 : _a.forEach((childId) => {
        var _a2;
        return dfs(childId, (_a2 = node.style) == null ? void 0 : _a2.color);
      });
    };
    nodes.filter((node) => node.depth === 1).forEach((rootNode) => dfs(rootNode.id));
    return input;
  }
};
var AssignColorByBranchTransform = _AssignColorByBranchTransform;
AssignColorByBranchTransform.defaultOptions = {
  colors: import_palette.DEFAULT_COLOR_PALETTE
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  AssignColorByBranchTransform
});
