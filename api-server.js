const http = require('http');
const { render } = require('./dist/cjs/index.js');

console.log('🚀 启动 GPT-Vis SSR API 服务器...');
console.log('Node.js 版本:', process.version);
console.log('✅ 真实 GPT-Vis SSR 渲染引擎已加载');

// 创建 HTTP 服务器
const server = http.createServer(async (req, res) => {
  const url = require('url').parse(req.url, true);
  
  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  try {
    if (url.pathname === '/') {
      // API 主页
      const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>GPT-Vis SSR API 服务器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        .status { padding: 15px; margin: 15px 0; border-radius: 6px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 GPT-Vis SSR API 服务器</h1>
        
        <div class="status success">
            <strong>✅ API 服务器状态:</strong> 运行中<br>
            <strong>🔧 渲染引擎:</strong> 真实 GPT-Vis SSR<br>
            <strong>📊 功能:</strong> 完全可用
        </div>
        
        <h2>📋 API 接口</h2>
        <p><a href="/api/health" class="btn">健康检查</a></p>
        <p><a href="/api/info" class="btn">系统信息</a></p>
        
        <h2>🔧 使用示例</h2>
        <h3>生成线图:</h3>
        <div class="code">
curl -X POST http://localhost:3001/api/chart \\
  -H "Content-Type: application/json" \\
  -d '{
    "type": "line",
    "data": [
      {"time": "2020", "value": 100},
      {"time": "2021", "value": 120},
      {"time": "2022", "value": 150}
    ],
    "width": 600,
    "height": 400
  }' \\
  --output line-chart.png
        </div>
        
        <h3>生成柱状图:</h3>
        <div class="code">
curl -X POST http://localhost:3001/api/chart \\
  -H "Content-Type: application/json" \\
  -d '{
    "type": "bar",
    "data": [
      {"category": "A", "value": 30},
      {"category": "B", "value": 45},
      {"category": "C", "value": 25}
    ]
  }' \\
  --output bar-chart.png
        </div>
    </div>
</body>
</html>`;
      
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(html);
      
    } else if (url.pathname === '/api/chart' && req.method === 'POST') {
      // 生成图表 API
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', async () => {
        try {
          const options = JSON.parse(body);
          
          if (!options.type) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: '缺少图表类型参数 (type)' }));
            return;
          }

          console.log(`📊 API 请求生成 ${options.type} 图表...`);
          const startTime = Date.now();
          
          const vis = await render(options);
          const buffer = vis.toBuffer();
          
          const renderTime = Date.now() - startTime;
          console.log(`✅ ${options.type} 图表生成完成，耗时: ${renderTime}ms，大小: ${buffer.length} bytes`);

          res.writeHead(200, { 
            'Content-Type': 'image/png',
            'Content-Length': buffer.length,
            'X-Render-Time': renderTime + 'ms',
            'X-Chart-Type': options.type,
            'X-Chart-Size': buffer.length + ' bytes'
          });
          res.end(buffer);

        } catch (error) {
          console.error('❌ API 图表生成错误:', error);
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ 
            error: '图表生成失败', 
            message: error.message,
            timestamp: new Date().toISOString()
          }));
        }
      });
      
    } else if (url.pathname === '/api/health') {
      // 健康检查
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'healthy',
        service: 'gpt-vis-ssr-api',
        renderEngine: 'real',
        nodeVersion: process.version,
        timestamp: new Date().toISOString(),
        uptime: Math.floor(process.uptime()),
        memory: process.memoryUsage()
      }));
      
    } else if (url.pathname === '/api/info') {
      // 系统信息
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        service: 'gpt-vis-ssr-api',
        version: '1.0.0',
        renderEngine: {
          type: 'real',
          description: '真实 AntV GPT-Vis SSR 渲染引擎',
          version: '0.1.6'
        },
        system: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
          uptime: Math.floor(process.uptime()),
          memory: process.memoryUsage()
        },
        supportedCharts: [
          'line', 'bar', 'column', 'pie', 'area', 'scatter',
          'radar', 'boxplot', 'histogram', 'violin', 'funnel',
          'sankey', 'treemap', 'liquid', 'word-cloud',
          'network-graph', 'flow-diagram', 'mind-map',
          'organization-chart', 'fishbone-diagram', 'dual-axes', 'venn'
        ],
        endpoints: [
          'POST /api/chart - 生成图表',
          'GET /api/health - 健康检查',
          'GET /api/info - 系统信息'
        ],
        timestamp: new Date().toISOString()
      }, null, 2));
      
    } else {
      // 404
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ 
        error: '接口不存在',
        availableEndpoints: [
          'GET /',
          'POST /api/chart',
          'GET /api/health',
          'GET /api/info'
        ]
      }));
    }
    
  } catch (error) {
    console.error('❌ 请求处理错误:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      error: '内部服务器错误',
      message: error.message,
      timestamp: new Date().toISOString()
    }));
  }
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`\n🎉 API 服务器启动成功！`);
  console.log(`📍 访问地址: http://localhost:${PORT}`);
  console.log(`🔧 渲染引擎: 真实 GPT-Vis SSR 渲染引擎`);
  console.log(`📊 API 端点: http://localhost:${PORT}/api/chart`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/api/health`);
  console.log(`\n示例 API 调用:`);
  console.log(`curl -X POST http://localhost:${PORT}/api/chart \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -d '{"type":"line","data":[{"time":"2020","value":100}]}' \\`);
  console.log(`  --output chart.png`);
  console.log(`\n✨ API 服务器正在运行，按 Ctrl+C 停止服务`);
});

process.on('SIGINT', () => {
  console.log('\n👋 正在关闭 API 服务器...');
  server.close(() => {
    console.log('✅ API 服务器已关闭');
    process.exit(0);
  });
});
