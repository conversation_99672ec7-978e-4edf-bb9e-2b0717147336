var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/scatter.ts
var scatter_exports = {};
__export(scatter_exports, {
  Scatter: () => Scatter
});
module.exports = __toCommonJS(scatter_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Scatter(options) {
  const {
    data,
    title,
    width = 600,
    height = 400,
    axisYTitle,
    axisXTitle,
    theme = "default"
  } = options;
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "point",
    theme: import_theme.THEME_MAP[theme],
    data,
    width,
    height,
    title,
    encode: {
      x: "x",
      y: "y"
      // shape: 'point',
    },
    axis: {
      x: {
        title: axisXTitle
      },
      y: {
        title: axisYTitle
      }
    },
    insetRight: 4,
    style: { lineWidth: 1 },
    legend: { size: false },
    animate: false,
    tooltip: false
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Scatter
});
