import { CommonOptions } from './types';
export type LiquidOptions = CommonOptions & {
    /**
     * Title of the liquid chart.
     */
    title?: string;
    /**
     * The percentage value to display in the liquid chart.
     * This should be a number between 0 and 1, where 1 represents 100%.
     * For example, 0.75 represents 75%.
     */
    percent: number;
    /**
     * Shape of the liquid chart.
     * Options are 'rect', 'circle', 'pin', or 'triangle'.
     */
    shape?: 'rect' | 'circle' | 'pin' | 'triangle';
};
export declare function Liquid(options: LiquidOptions): Promise<import("@antv/g2-ssr").Chart>;
