import { CommonOptions } from './types';
export type TreeGraphData = {
    name: string;
    description: string;
    children?: TreeGraphData[];
};
export type OrganizationChartOptions = CommonOptions & {
    /**
     * Data for the organization chart.
     */
    data: TreeGraphData;
    /**
     * The orient of the organization chart.
     * Can be 'vertical' or 'horizontal'.
     * Default is 'vertical'.
     */
    orient?: 'vertical' | 'horizontal';
};
/**
 * 组织架构图
 * @param options
 * @returns
 */
export declare function OrganizationChart(options: OrganizationChartOptions): Promise<import("@antv/g6-ssr").Graph>;
