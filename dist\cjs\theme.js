var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/theme.ts
var theme_exports = {};
__export(theme_exports, {
  G6THEME_MAP: () => G6THEME_MAP,
  THEME_MAP: () => THEME_MAP
});
module.exports = __toCommonJS(theme_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var import_util = require("./util");
var { register, ExtensionCategory } = import_g6_ssr.G6;
register(ExtensionCategory.TRANSFORM, "assign-color-by-branch", import_util.AssignColorByBranchTransform);
var DEFAULT_THEME = {
  type: "light",
  view: {
    viewFill: "#FFF",
    plotFill: "transparent",
    mainFill: "transparent",
    contentFill: "transparent"
  },
  interval: {
    rect: {
      fillOpacity: 0.8
    }
  },
  line: {
    line: {
      lineWidth: 2
    }
  },
  area: {
    area: {
      fillOpacity: 0.6
    }
  },
  point: {
    point: {
      lineWidth: 1
    }
  }
};
var ACADEMY_THEME = {
  type: "academy",
  view: {
    viewFill: "#FFF",
    plotFill: "transparent",
    mainFill: "transparent",
    contentFill: "transparent"
  },
  interval: {
    rect: {
      fillOpacity: 0.8
    }
  },
  line: {
    line: {
      lineWidth: 2
    }
  },
  area: {
    area: {
      fillOpacity: 0.6
    }
  },
  point: {
    point: {
      lineWidth: 1
    }
  }
};
var THEME_MAP = {
  default: DEFAULT_THEME,
  academy: ACADEMY_THEME
};
var G6THEME_MAP = {
  default: {
    type: "assign-color-by-branch",
    colors: import_util.DEFAULT_COLOR_PALETTE
  },
  academy: {
    type: "assign-color-by-branch",
    colors: import_util.ACADEMY_COLOR_PALETTE
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  G6THEME_MAP,
  THEME_MAP
});
