var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/organization-chart.ts
var organization_chart_exports = {};
__export(organization_chart_exports, {
  OrganizationChart: () => OrganizationChart
});
module.exports = __toCommonJS(organization_chart_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var import_theme = require("../theme");
var import_util = require("../util");
var { register, ExtensionCategory } = import_g6_ssr.G6;
register(ExtensionCategory.NODE, "organization-chart-node", import_util.OrganizationChartNode);
async function OrganizationChart(options) {
  const { data, width = 600, height = 400, theme = "default", orient = "vertical" } = options;
  const dataParse = (0, import_util.treeToGraphData)(data);
  const isHorizontal = orient === "horizontal";
  return await (0, import_g6_ssr.createGraph)({
    waitForRender: 300,
    width,
    height,
    data: dataParse,
    devicePixelRatio: 3,
    padding: 24,
    autoFit: {
      type: "view"
    },
    node: {
      type: "organization-chart-node",
      style: {
        labelPlacement: "center",
        lineWidth: 0,
        ports: isHorizontal ? [{ placement: "left" }, { placement: "right" }] : [{ placement: "top" }, { placement: "bottom" }],
        radius: 4,
        shadowBlur: 10,
        shadowColor: "#e0e0e0",
        shadowOffsetX: 3,
        size: [200, 60]
      },
      palette: {
        type: "group",
        field: (d) => d.data.name,
        color: import_theme.G6THEME_MAP[theme].colors
      }
    },
    edge: {
      type: "polyline",
      style: {
        router: {
          type: "orth"
        },
        stroke: "#99ADD1"
      }
    },
    layout: {
      type: "dagre",
      rankdir: isHorizontal ? "LR" : "TB"
    },
    animation: false,
    behaviors: []
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  OrganizationChart
});
