var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  render: () => render
});
module.exports = __toCommonJS(src_exports);
var import_area = require("./vis/area");
var import_bar = require("./vis/bar");
var import_boxplot = require("./vis/boxplot");
var import_column = require("./vis/column");
var import_dual_axes = require("./vis/dual-axes");
var import_fishbone_diagram = require("./vis/fishbone-diagram");
var import_flow_diagram = require("./vis/flow-diagram");
var import_funnel = require("./vis/funnel");
var import_histogram = require("./vis/histogram");
var import_line = require("./vis/line");
var import_liquid = require("./vis/liquid");
var import_mind_map = require("./vis/mind-map");
var import_network_graph = require("./vis/network-graph");
var import_organization_chart = require("./vis/organization-chart");
var import_pie = require("./vis/pie");
var import_radar = require("./vis/radar");
var import_sankey = require("./vis/sankey");
var import_scatter = require("./vis/scatter");
var import_treemap = require("./vis/treemap");
var import_venn = require("./vis/venn");
var import_violin = require("./vis/violin");
var import_word_cloud = require("./vis/word-cloud");
var VIS = {
  area: import_area.Area,
  bar: import_bar.Bar,
  boxplot: import_boxplot.Boxplot,
  column: import_column.Column,
  "dual-axes": import_dual_axes.DualAxes,
  "fishbone-diagram": import_fishbone_diagram.FishboneDiagram,
  "flow-diagram": import_flow_diagram.FlowDiagram,
  funnel: import_funnel.Funnel,
  histogram: import_histogram.Histogram,
  line: import_line.Line,
  liquid: import_liquid.Liquid,
  "mind-map": import_mind_map.MindMap,
  "network-graph": import_network_graph.NetworkGraph,
  "organization-chart": import_organization_chart.OrganizationChart,
  pie: import_pie.Pie,
  radar: import_radar.Radar,
  sankey: import_sankey.Sankey,
  scatter: import_scatter.Scatter,
  treemap: import_treemap.Treemap,
  violin: import_violin.Violin,
  venn: import_venn.Venn,
  "word-cloud": import_word_cloud.WordCloud
};
async function render(options) {
  const { type, ...rest } = options;
  const renderVis = VIS[type];
  if (!renderVis) {
    throw new Error(`Unknown chart type: ${type}`);
  }
  return await renderVis(rest);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  render
});
