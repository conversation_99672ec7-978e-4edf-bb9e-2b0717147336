var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/boxplot.ts
var boxplot_exports = {};
__export(boxplot_exports, {
  Boxplot: () => Boxplot
});
module.exports = __toCommonJS(boxplot_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Boxplot(options) {
  var _a;
  const {
    data,
    title,
    width = 600,
    height = 400,
    axisYTitle,
    axisXTitle,
    theme = "default"
  } = options;
  const hasGroupField = ((_a = (data || [])[0]) == null ? void 0 : _a.group) !== void 0;
  let encode = {};
  if (hasGroupField) {
    encode = {
      x: "category",
      y: "value",
      color: "group",
      series: "group"
    };
  } else {
    encode = {
      x: "category",
      y: "value",
      color: "category"
    };
  }
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "boxplot",
    theme: import_theme.THEME_MAP[theme],
    width,
    height,
    title,
    autoFit: true,
    data,
    axis: {
      y: {
        title: axisYTitle || false
      },
      x: {
        title: axisXTitle || false
      }
    },
    encode,
    scale: {
      x: { paddingInner: 0.6, paddingOuter: 0.3 },
      series: { paddingInner: 0.3, paddingOuter: 0.1 }
    },
    style: { stroke: "black" }
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Boxplot
});
