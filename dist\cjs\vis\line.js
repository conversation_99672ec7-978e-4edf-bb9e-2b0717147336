var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/line.ts
var line_exports = {};
__export(line_exports, {
  Line: () => Line
});
module.exports = __toCommonJS(line_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Line(options) {
  var _a;
  const {
    data,
    title,
    width = 600,
    height = 400,
    axisYTitle,
    axisXTitle,
    theme = "default"
  } = options;
  const hasGroupField = ((_a = (data || [])[0]) == null ? void 0 : _a.group) !== void 0;
  let encode = {};
  if (hasGroupField) {
    encode = { x: "time", y: "value", color: "group" };
  } else {
    encode = { x: "time", y: "value" };
  }
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "view",
    title,
    data,
    width,
    height,
    encode,
    theme: import_theme.THEME_MAP[theme],
    insetRight: 12,
    insetTop: 4,
    style: { minHeight: 1 },
    axis: {
      y: {
        title: axisYTitle || false
      },
      x: {
        title: axisXTitle || false
      }
    },
    children: [
      {
        type: "line",
        style: {
          lineWidth: 2
        },
        labels: [
          {
            text: "value",
            style: { textAlign: "center", dy: -12 },
            transform: [{ type: "overlapDodgeY" }]
          }
        ]
      },
      {
        type: "point",
        encode: { shape: "point" },
        style: { fill: "white", lineWidth: 1 }
      }
    ]
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Line
});
