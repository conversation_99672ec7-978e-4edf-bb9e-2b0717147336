function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
import { G6 } from '@antv/g6-ssr';
import { DEFAULT_COLOR_PALETTE } from "./palette";
var BaseTransform = G6.BaseTransform;
export var AssignColorByBranchTransform = /*#__PURE__*/function (_BaseTransform) {
  _inherits(AssignColorByBranchTransform, _BaseTransform);
  var _super = _createSuper(AssignColorByBranchTransform);
  function AssignColorByBranchTransform(context, options) {
    _classCallCheck(this, AssignColorByBranchTransform);
    return _super.call(this, context, Object.assign({}, AssignColorByBranchTransform.defaultOptions, options));
  }
  _createClass(AssignColorByBranchTransform, [{
    key: "beforeDraw",
    value: function beforeDraw(input) {
      var _this = this;
      var nodes = this.context.model.getNodeData();
      if (nodes.length === 0) return input;
      var colorIndex = 0;
      var dfs = function dfs(nodeId, color) {
        var _node$children;
        var node = nodes.find(function (datum) {
          return datum.id == nodeId;
        });
        if (!node) return;
        node.style || (node.style = {});
        node.style.color = color || _this.options.colors[colorIndex++ % _this.options.colors.length];
        (_node$children = node.children) === null || _node$children === void 0 || _node$children.forEach(function (childId) {
          var _node$style;
          return dfs(childId, (_node$style = node.style) === null || _node$style === void 0 ? void 0 : _node$style.color);
        });
      };
      // @ts-ignore
      nodes.filter(function (node) {
        return node.depth === 1;
      }).forEach(function (rootNode) {
        return dfs(rootNode.id);
      });
      return input;
    }
  }]);
  return AssignColorByBranchTransform;
}(BaseTransform);
_defineProperty(AssignColorByBranchTransform, "defaultOptions", {
  colors: DEFAULT_COLOR_PALETTE
});