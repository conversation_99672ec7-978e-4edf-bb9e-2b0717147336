const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🚀 启动工作服务器...');
console.log('Node.js 版本:', process.version);

// 尝试加载真实的渲染函数
let render;
let renderStatus = 'unknown';

try {
  console.log('尝试加载 gpt-vis-ssr...');
  const gptVis = require('./dist/cjs/index.js');
  render = gptVis.render;
  renderStatus = 'real';
  console.log('✅ 成功加载真实的 gpt-vis-ssr 渲染引擎');
} catch (error) {
  console.log('❌ 加载 gpt-vis-ssr 失败:', error.message);
  console.log('使用增强的模拟渲染引擎...');
  renderStatus = 'mock';
  
  // 创建一个更好的模拟渲染函数
  render = async function(options) {
    console.log(`模拟渲染 ${options.type} 图表...`);
    
    // 创建一个有效的 PNG 图片 (1x1 像素，但有正确的结构)
    const createMinimalPNG = () => {
      return Buffer.from([
        // PNG 签名
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
        // IHDR chunk
        0x00, 0x00, 0x00, 0x0D, // 长度
        0x49, 0x48, 0x44, 0x52, // IHDR
        0x00, 0x00, 0x01, 0x90, // 宽度 400
        0x00, 0x00, 0x01, 0x2C, // 高度 300
        0x08, 0x02, 0x00, 0x00, 0x00, // 8位深度，RGB
        0x4A, 0x7D, 0x64, 0x2F, // CRC
        // IDAT chunk (最小数据)
        0x00, 0x00, 0x00, 0x0C, // 长度
        0x49, 0x44, 0x41, 0x54, // IDAT
        0x78, 0x9C, 0x62, 0x00, 0x02, 0x00, 0x00, 0x05, 0x00, 0x01, 0x0D, 0x0A, // 压缩数据
        0x2D, 0xB4, 0x0C, 0x21, // CRC
        // IEND chunk
        0x00, 0x00, 0x00, 0x00, // 长度
        0x49, 0x45, 0x4E, 0x44, // IEND
        0xAE, 0x42, 0x60, 0x82  // CRC
      ]);
    };
    
    const buffer = createMinimalPNG();
    
    return {
      toBuffer: () => buffer,
      exportToFile: (filename) => {
        fs.writeFileSync(filename, buffer);
      },
      toDataURL: () => {
        return 'data:image/png;base64,' + buffer.toString('base64');
      }
    };
  };
}

// 创建 HTTP 服务器
const server = http.createServer(async (req, res) => {
  const url = require('url').parse(req.url, true);
  
  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  try {
    if (url.pathname === '/') {
      // 主页
      const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT-Vis SSR 工作服务器</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 6px; 
            border-left: 4px solid; 
        }
        .success { 
            background: #f6ffed; 
            border-color: #52c41a; 
            color: #389e0d; 
        }
        .warning { 
            background: #fffbe6; 
            border-color: #faad14; 
            color: #d48806; 
        }
        .test-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 15px; 
            margin: 20px 0; 
        }
        .test-card { 
            background: #fafafa; 
            border: 1px solid #d9d9d9; 
            border-radius: 6px; 
            padding: 20px; 
            text-align: center; 
        }
        .test-card h3 { 
            margin: 0 0 15px 0; 
            color: #262626; 
        }
        .btn { 
            display: inline-block; 
            padding: 8px 16px; 
            background: #1890ff; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            border: none; 
            cursor: pointer; 
            margin: 5px; 
        }
        .btn:hover { 
            background: #40a9ff; 
        }
        .btn-success { 
            background: #52c41a; 
        }
        .btn-success:hover { 
            background: #73d13d; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 GPT-Vis SSR 工作服务器</h1>
        
        <div class="status ${renderStatus === 'real' ? 'success' : 'warning'}">
            <strong>🔧 渲染引擎:</strong> ${renderStatus === 'real' ? '✅ 真实渲染引擎 (gpt-vis-ssr)' : '⚠️ 模拟渲染引擎'}
        </div>
        
        <div class="status success">
            <strong>📋 系统信息:</strong><br>
            • Node.js 版本: ${process.version}<br>
            • 服务器状态: 运行中<br>
            • 启动时间: ${new Date().toLocaleString('zh-CN')}
        </div>
        
        <h2>📊 图表生成测试</h2>
        <div class="test-grid">
            <div class="test-card">
                <h3>线图</h3>
                <a href="/chart/line" class="btn" target="_blank">生成线图</a>
            </div>
            <div class="test-card">
                <h3>柱状图</h3>
                <a href="/chart/bar" class="btn" target="_blank">生成柱状图</a>
            </div>
            <div class="test-card">
                <h3>饼图</h3>
                <a href="/chart/pie" class="btn" target="_blank">生成饼图</a>
            </div>
            <div class="test-card">
                <h3>面积图</h3>
                <a href="/chart/area" class="btn" target="_blank">生成面积图</a>
            </div>
        </div>
        
        <h2>🔍 API 接口测试</h2>
        <div class="test-grid">
            <div class="test-card">
                <h3>健康检查</h3>
                <a href="/api/health" class="btn btn-success" target="_blank">检查状态</a>
            </div>
            <div class="test-card">
                <h3>系统信息</h3>
                <a href="/api/info" class="btn btn-success" target="_blank">查看信息</a>
            </div>
        </div>
        
        <h2>📝 使用说明</h2>
        <div class="status success">
            <strong>✅ 服务器已成功启动并运行</strong><br>
            • 点击上方按钮测试图表生成功能<br>
            • 生成的图片将直接在浏览器中显示<br>
            • API 接口返回 JSON 格式的状态信息<br>
            ${renderStatus === 'mock' ? '• 当前使用模拟渲染，图片为简单的 PNG 格式' : '• 当前使用真实渲染引擎，可生成完整图表'}
        </div>
    </div>
</body>
</html>`;
      
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(html);
      
    } else if (url.pathname.startsWith('/chart/')) {
      // 生成图表
      const chartType = url.pathname.split('/')[2];
      
      const sampleData = {
        line: [
          { time: '2020', value: 100 },
          { time: '2021', value: 120 },
          { time: '2022', value: 150 },
          { time: '2023', value: 180 },
          { time: '2024', value: 200 }
        ],
        bar: [
          { category: 'A', value: 30 },
          { category: 'B', value: 45 },
          { category: 'C', value: 25 },
          { category: 'D', value: 60 }
        ],
        pie: [
          { category: 'Desktop', value: 60 },
          { category: 'Mobile', value: 30 },
          { category: 'Tablet', value: 10 }
        ],
        area: [
          { time: '2020', value: 100 },
          { time: '2021', value: 120 },
          { time: '2022', value: 150 },
          { time: '2023', value: 180 }
        ]
      };
      
      const options = {
        type: chartType,
        data: sampleData[chartType] || sampleData.line,
        width: 600,
        height: 400
      };
      
      console.log(`📊 生成 ${chartType} 图表...`);
      const vis = await render(options);
      const buffer = vis.toBuffer();
      
      res.writeHead(200, { 
        'Content-Type': 'image/png',
        'Content-Length': buffer.length,
        'Content-Disposition': `inline; filename="${chartType}-chart.png"`,
        'Cache-Control': 'no-cache'
      });
      res.end(buffer);
      
    } else if (url.pathname === '/api/health') {
      // 健康检查
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'healthy',
        service: 'gpt-vis-ssr-working-server',
        renderEngine: renderStatus,
        nodeVersion: process.version,
        timestamp: new Date().toISOString(),
        uptime: Math.floor(process.uptime())
      }));
      
    } else if (url.pathname === '/api/info') {
      // 系统信息
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        service: 'gpt-vis-ssr-working-server',
        version: '1.0.0',
        renderEngine: {
          type: renderStatus,
          description: renderStatus === 'real' ? '真实 gpt-vis-ssr 渲染引擎' : '模拟渲染引擎'
        },
        system: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
          uptime: Math.floor(process.uptime()),
          memory: process.memoryUsage()
        },
        supportedCharts: ['line', 'bar', 'pie', 'area'],
        timestamp: new Date().toISOString()
      }, null, 2));
      
    } else {
      // 404
      res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <html>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1>404 - 页面未找到</h1>
            <p><a href="/">返回主页</a></p>
          </body>
        </html>
      `);
    }
    
  } catch (error) {
    console.error('❌ 请求处理错误:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      error: '内部服务器错误',
      message: error.message,
      timestamp: new Date().toISOString()
    }));
  }
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`\n🎉 工作服务器启动成功！`);
  console.log(`📍 访问地址: http://localhost:${PORT}`);
  console.log(`🔧 渲染引擎: ${renderStatus === 'real' ? '真实渲染' : '模拟渲染'}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/api/health`);
  console.log(`\n✨ 服务器正在运行，按 Ctrl+C 停止服务`);
});

process.on('SIGINT', () => {
  console.log('\n👋 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
