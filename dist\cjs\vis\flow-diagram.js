var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/flow-diagram.ts
var flow_diagram_exports = {};
__export(flow_diagram_exports, {
  FlowDiagram: () => FlowDiagram
});
module.exports = __toCommonJS(flow_diagram_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var import_theme = require("../theme");
var MAX_WIDTH = 110;
async function FlowDiagram(options) {
  const { data, width = 600, height = 400, theme = "default" } = options;
  const graphData = {
    nodes: data.nodes.map((node) => ({ ...node, id: node.name })),
    edges: data.edges.map((edge) => ({ ...edge, id: `${edge.source}-${edge.target}` }))
  };
  return await (0, import_g6_ssr.createGraph)({
    data: graphData,
    width,
    height,
    devicePixelRatio: 3,
    autoFit: "view",
    padding: 20,
    animation: false,
    node: {
      type: "rect",
      style: {
        // @ts-ignore
        size: [110, 38],
        // size: (d: any) => [d.name.length * 15 + 30, 35],
        radius: 6,
        // @ts-ignore
        iconText: (d) => d.name,
        iconFontSize: 12,
        iconFontWeight: 800,
        iconWordWrapWidth: MAX_WIDTH - 5,
        iconTextOverflow: "ellipsis",
        iconWordWrap: true,
        iconMaxLines: 2
      }
    },
    edge: {
      type: "polyline",
      style: {
        lineWidth: 2,
        radius: 10,
        stroke: "#99ADD1",
        endArrow: true,
        // @ts-ignore
        labelText: (d) => d.name,
        labelFill: "#555555",
        labelFontWeight: 800,
        labelBackground: true,
        labelBackgroundFill: "rgba(255,255,255,0.6)",
        labelBackgroundOpacity: 1,
        labelBackgroundLineWidth: 2,
        labelPadding: [2, 5],
        labelBackgroundRadius: 2,
        router: {
          type: "orth"
        }
      }
    },
    layout: {
      type: "dagre",
      rankdir: "LR"
    },
    transforms: [import_theme.G6THEME_MAP[theme]]
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  FlowDiagram
});
