var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/network-graph.ts
var network_graph_exports = {};
__export(network_graph_exports, {
  NetworkGraph: () => NetworkGraph
});
module.exports = __toCommonJS(network_graph_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var import_theme = require("../theme");
var { register, BaseTransform, ExtensionCategory } = import_g6_ssr.G6;
async function NetworkGraph(options) {
  const { data, width = 600, height = 400, theme = "default" } = options;
  const graphData = {
    nodes: data.nodes.map((node) => ({ ...node, id: node.name })),
    edges: data.edges.map((edge) => ({ ...edge, id: `${edge.source}-${edge.target}` }))
  };
  return await (0, import_g6_ssr.createGraph)({
    data: graphData,
    width,
    height,
    devicePixelRatio: 3,
    autoFit: "view",
    padding: 20,
    animation: false,
    node: {
      type: "circle",
      style: {
        size: 20,
        // @ts-ignore
        labelText: (d) => d.name,
        labelSize: 10,
        labelFontSize: 10,
        labelBackground: true
      }
    },
    edge: {
      style: {
        // @ts-ignore
        labelText: (d) => d.name,
        labelFontSize: 10,
        labelBackground: true,
        endArrow: true
      },
      animation: { enter: false }
    },
    layout: {
      type: "force-atlas2",
      preventOverlap: true,
      kr: 600
    },
    transforms: ["process-parallel-edges", import_theme.G6THEME_MAP[theme]]
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  NetworkGraph
});
