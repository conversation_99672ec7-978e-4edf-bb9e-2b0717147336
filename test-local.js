const { render } = require('./dist/cjs/index.js');
const fs = require('fs');
const path = require('path');

async function testLocalDeployment() {
  console.log('开始测试 gpt-vis-ssr 本地部署...');
  
  try {
    // 测试线图
    console.log('1. 测试线图渲染...');
    const lineOptions = {
      type: 'line',
      data: [
        { time: 2018, value: 91.9 },
        { time: 2019, value: 99.1 },
        { time: 2020, value: 101.6 },
        { time: 2021, value: 114.4 },
        { time: 2022, value: 121 },
      ],
    };

    const lineVis = await render(lineOptions);
    const lineBuffer = lineVis.toBuffer();
    
    // 保存到文件
    const outputDir = './output';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir);
    }
    
    fs.writeFileSync(path.join(outputDir, 'line-chart.png'), lineBuffer);
    console.log('✅ 线图渲染成功，已保存到 output/line-chart.png');

    // 测试柱状图
    console.log('2. 测试柱状图渲染...');
    const barOptions = {
      type: 'bar',
      data: [
        { category: 'A', value: 30 },
        { category: 'B', value: 45 },
        { category: 'C', value: 25 },
        { category: 'D', value: 60 },
      ],
    };

    const barVis = await render(barOptions);
    const barBuffer = barVis.toBuffer();
    fs.writeFileSync(path.join(outputDir, 'bar-chart.png'), barBuffer);
    console.log('✅ 柱状图渲染成功，已保存到 output/bar-chart.png');

    // 测试饼图
    console.log('3. 测试饼图渲染...');
    const pieOptions = {
      type: 'pie',
      data: [
        { category: 'A', value: 30 },
        { category: 'B', value: 25 },
        { category: 'C', value: 20 },
        { category: 'D', value: 25 },
      ],
    };

    const pieVis = await render(pieOptions);
    const pieBuffer = pieVis.toBuffer();
    fs.writeFileSync(path.join(outputDir, 'pie-chart.png'), pieBuffer);
    console.log('✅ 饼图渲染成功，已保存到 output/pie-chart.png');

    console.log('\n🎉 所有测试通过！gpt-vis-ssr 本地部署成功！');
    console.log('生成的图表文件保存在 output/ 目录中');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误信息:', error);
  }
}

// 运行测试
testLocalDeployment();
