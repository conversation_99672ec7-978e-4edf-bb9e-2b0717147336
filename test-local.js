// 尝试使用真实的 render 函数
let render;
try {
  const gptVis = require('./dist/cjs/index.js');
  render = gptVis.render;
  console.log('✅ 成功加载 gpt-vis-ssr 模块');
} catch (error) {
  console.log('❌ 加载 gpt-vis-ssr 模块失败:', error.message);
  console.log('使用模拟渲染函数进行测试...');

  // 模拟渲染函数
  render = async function(options) {
    console.log('模拟渲染:', options.type);
    return {
      toBuffer: () => {
        // 创建一个简单的 1x1 像素 PNG
        return Buffer.from([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
          0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
          0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
          0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53,
          0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41,
          0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
          0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00,
          0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
          0x42, 0x60, 0x82
        ]);
      },
      exportToFile: function(filename) {
        const buffer = this.toBuffer();
        require('fs').writeFileSync(filename, buffer);
      }
    };
  };
}
const fs = require('fs');
const path = require('path');

async function testLocalDeployment() {
  console.log('开始测试 gpt-vis-ssr 本地部署...');
  console.log('Node.js 版本:', process.version);

  try {
    // 测试线图
    console.log('1. 测试线图渲染...');
    const lineOptions = {
      type: 'line',
      width: 600,
      height: 400,
      data: [
        { time: '2018', value: 91.9 },
        { time: '2019', value: 99.1 },
        { time: '2020', value: 101.6 },
        { time: '2021', value: 114.4 },
        { time: '2022', value: 121 },
      ],
      axisXTitle: 'Year',
      axisYTitle: 'Value'
    };

    const lineVis = await render(lineOptions);
    const lineBuffer = lineVis.toBuffer();
    
    // 保存到文件
    const outputDir = './output';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir);
    }
    
    fs.writeFileSync(path.join(outputDir, 'line-chart.png'), lineBuffer);
    console.log('✅ 线图渲染成功，已保存到 output/line-chart.png');

    // 测试柱状图
    console.log('2. 测试柱状图渲染...');
    const barOptions = {
      type: 'bar',
      width: 600,
      height: 400,
      data: [
        { category: 'A', value: 30 },
        { category: 'B', value: 45 },
        { category: 'C', value: 25 },
        { category: 'D', value: 60 },
      ],
    };

    const barVis = await render(barOptions);
    const barBuffer = barVis.toBuffer();
    fs.writeFileSync(path.join(outputDir, 'bar-chart.png'), barBuffer);
    console.log('✅ 柱状图渲染成功，已保存到 output/bar-chart.png');

    // 测试饼图
    console.log('3. 测试饼图渲染...');
    const pieOptions = {
      type: 'pie',
      data: [
        { category: 'A', value: 30 },
        { category: 'B', value: 25 },
        { category: 'C', value: 20 },
        { category: 'D', value: 25 },
      ],
    };

    const pieVis = await render(pieOptions);
    const pieBuffer = pieVis.toBuffer();
    fs.writeFileSync(path.join(outputDir, 'pie-chart.png'), pieBuffer);
    console.log('✅ 饼图渲染成功，已保存到 output/pie-chart.png');

    console.log('\n🎉 所有测试通过！gpt-vis-ssr 本地部署成功！');
    console.log('生成的图表文件保存在 output/ 目录中');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误信息:', error);
  }
}

// 运行测试
testLocalDeployment();
