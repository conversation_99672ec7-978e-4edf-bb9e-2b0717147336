var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/util/graph-data.ts
var graph_data_exports = {};
__export(graph_data_exports, {
  treeToGraphData: () => treeToGraphData
});
module.exports = __toCommonJS(graph_data_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var { treeToGraphData: treeToGraphDataG6 } = import_g6_ssr.G6;
function treeToGraphData(data) {
  return treeToGraphDataG6(data, {
    getNodeData: (datum, depth) => {
      const { children, ...restDatum } = datum;
      return {
        id: datum.name,
        depth,
        data: {
          depth,
          ...restDatum
        }
      };
    },
    getEdgeData: (source, target) => ({
      source: source.name,
      target: target.name
    })
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  treeToGraphData
});
