var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/dual-axes.ts
var dual_axes_exports = {};
__export(dual_axes_exports, {
  DualAxes: () => DualAxes
});
module.exports = __toCommonJS(dual_axes_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function DualAxes(options) {
  const { series, categories, title, width = 600, height = 400, theme = "default" } = options;
  let ChartType;
  ((ChartType2) => {
    ChartType2["Column"] = "column";
    ChartType2["Line"] = "line";
  })(ChartType || (ChartType = {}));
  let radiusStyle = {};
  if (theme === "default") {
    radiusStyle = { radiusTopLeft: 4, radiusTopRight: 4 };
  }
  function transform(series2, categories2) {
    const newChildren = series2.sort((a, b) => {
      const ORDER = ["column", "line"];
      return ORDER.indexOf(a.type) - ORDER.indexOf(b.type);
    }).map((item) => {
      const { type, axisYTitle, ...others } = item;
      const baseConfig = {
        ...others,
        axis: { y: { title: axisYTitle } },
        encode: { x: "category", y: axisYTitle, color: () => axisYTitle },
        legend: {
          color: {
            itemMarker: (v) => {
              if (v === axisYTitle)
                return "smooth";
              return "rect";
            }
          }
        },
        data: void 0
      };
      if (type === "column" /* Column */) {
        return {
          ...baseConfig,
          type: "interval",
          style: { columnWidthRatio: 0.8, ...radiusStyle }
        };
      }
      if (type === "line" /* Line */) {
        return {
          ...baseConfig,
          type,
          axis: { y: { position: "right", title: axisYTitle } },
          encode: { x: "category", y: axisYTitle, shape: "smooth", color: () => axisYTitle },
          style: { lineWidth: 2 },
          scale: { y: { independent: true } }
        };
      }
      return baseConfig;
    });
    const newData = categories2.map((item, index) => {
      const temp = {
        category: item
      };
      series2.forEach((s, i) => {
        const defaultYField = s.axisYTitle || `value_${i + 1}`;
        temp[defaultYField] = s.data[index];
      });
      return temp;
    });
    const legendTypeList = series2.map((item) => {
      return item.type === "line" /* Line */ ? "smooth" : "rect";
    });
    return {
      children: newChildren,
      data: newData,
      legendTypeList
    };
  }
  const config = transform(series, categories);
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "view",
    theme: import_theme.THEME_MAP[theme],
    autoFit: true,
    title,
    width,
    height,
    ...config
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  DualAxes
});
