var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/fishbone-diagram.ts
var fishbone_diagram_exports = {};
__export(fishbone_diagram_exports, {
  FishboneDiagram: () => FishboneDiagram
});
module.exports = __toCommonJS(fishbone_diagram_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var import_canvas = require("canvas");
var import_theme = require("../theme");
var { treeToGraphData } = import_g6_ssr.G6;
var canvas = null;
var ctx = null;
var measureText = (style) => {
  if (!canvas) {
    canvas = (0, import_canvas.createCanvas)(0, 0);
    ctx = canvas.getContext("2d");
  }
  const font = [
    style.fontStyle || "normal",
    style.fontWeight || "normal",
    `${style.fontSize || 12}px`,
    style.fontFamily || "sans-serif"
  ].join(" ");
  ctx.font = font;
  return ctx.measureText(style.text).width;
};
var getNodeSize = (id, depth) => {
  const FONT_FAMILY = "system-ui, sans-serif";
  return depth === 0 ? [
    measureText({ text: id, fontSize: 24, fontWeight: "bold", fontFamily: FONT_FAMILY }) + 80,
    70
  ] : depth === 1 ? [measureText({ text: id, fontSize: 18, fontFamily: FONT_FAMILY }) + 50, 42] : [2, 30];
};
function visTreeData2GraphData(data) {
  return treeToGraphData(data, {
    getNodeData: (datum, depth) => {
      datum.id = datum.name;
      datum.depth = depth;
      if (!datum.children)
        return datum;
      const { children, ...restDatum } = datum;
      return {
        ...restDatum,
        children: children.map((child) => child.name)
      };
    },
    getEdgeData: (source, target) => ({
      source: source.name,
      target: target.name
    })
  });
}
async function FishboneDiagram(options) {
  const { data, width = 600, height = 400, theme = "default" } = options;
  const dataParse = visTreeData2GraphData(data);
  return await (0, import_g6_ssr.createGraph)({
    autoFit: {
      type: "view",
      options: {
        when: "overflow",
        direction: "x"
      }
    },
    width,
    height,
    data: dataParse,
    devicePixelRatio: 3,
    padding: 20,
    node: {
      type: "rect",
      // @ts-ignore
      style: (d) => {
        var _a;
        const style = {
          radius: 8,
          size: getNodeSize(d.id, d.depth),
          labelText: d.id,
          labelPlacement: "left",
          labelFontFamily: "Gill Sans"
        };
        if (d.depth === 0) {
          Object.assign(style, {
            fill: "#EFF0F0",
            labelFill: "#262626",
            labelFontWeight: "bold",
            labelFontSize: 24,
            labelOffsetY: 4,
            labelPlacement: "center",
            labelLineHeight: 24
          });
        } else if (d.depth === 1) {
          Object.assign(style, {
            labelFontSize: 18,
            labelFill: "#fff",
            labelFillOpacity: 0.9,
            labelOffsetY: 5,
            labelPlacement: "center",
            fill: (_a = d.style) == null ? void 0 : _a.color
          });
        } else {
          Object.assign(style, {
            fill: "transparent",
            labelFontSize: 16,
            labeFill: "#262626"
          });
        }
        return style;
      }
    },
    edge: {
      type: "polyline",
      style: {
        lineWidth: 3,
        // @ts-ignore
        stroke: function(data2) {
          return this.getNodeData(data2.target).style.color || "#99ADD1";
        }
      }
    },
    layout: {
      type: "fishbone",
      direction: "RL",
      hGap: 40,
      vGap: 60
    },
    transforms: [import_theme.G6THEME_MAP[theme]]
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  FishboneDiagram
});
