var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/pie.ts
var pie_exports = {};
__export(pie_exports, {
  Pie: () => Pie
});
module.exports = __toCommonJS(pie_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Pie(options) {
  const { data, title, width = 600, height = 400, innerRadius, theme = "default" } = options;
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "interval",
    theme: import_theme.THEME_MAP[theme],
    title,
    width,
    height,
    data,
    encode: { y: "value", color: "category" },
    transform: [{ type: "stackY" }],
    coordinate: {
      type: "theta",
      outerRadius: 0.95,
      innerRadius
    },
    style: {
      radius: 4,
      stroke: "#fff",
      lineWidth: 1
    },
    labels: [
      {
        text: (data2) => `${data2.category}: ${data2.value}`,
        position: "outside",
        radius: 0.85,
        fontSize: 12,
        transform: [{ type: "overlapHide" }]
      }
    ],
    legend: {
      color: { position: "bottom", layout: { justifyContent: "center" } }
    },
    animate: false,
    axis: false
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Pie
});
