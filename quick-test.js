console.log('🚀 快速测试 GPT-Vis SSR...');
console.log('Node.js 版本:', process.version);

// 测试模块加载
console.log('\n1. 测试模块加载...');

try {
  console.log('尝试加载 gpt-vis-ssr...');
  const gptVis = require('./dist/cjs/index.js');
  console.log('✅ 成功加载 gpt-vis-ssr 模块');
  console.log('可用的导出:', Object.keys(gptVis));
  
  if (gptVis.render) {
    console.log('✅ render 函数可用');
    
    // 测试简单的图表生成
    console.log('\n2. 测试图表生成...');
    
    const testOptions = {
      type: 'line',
      data: [
        { time: '2020', value: 100 },
        { time: '2021', value: 120 },
        { time: '2022', value: 150 }
      ],
      width: 400,
      height: 300
    };
    
    gptVis.render(testOptions)
      .then(vis => {
        console.log('✅ 图表渲染成功');
        console.log('返回对象方法:', Object.getOwnPropertyNames(vis));
        
        if (vis.toBuffer) {
          const buffer = vis.toBuffer();
          console.log('✅ 成功生成图片 Buffer，大小:', buffer.length, 'bytes');
          
          // 保存到文件
          const fs = require('fs');
          const outputDir = './test-output';
          if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir);
          }
          
          fs.writeFileSync('./test-output/test-chart.png', buffer);
          console.log('✅ 图片已保存到 test-output/test-chart.png');
          
          // 验证 PNG 文件头
          const pngSignature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
          if (buffer.subarray(0, 8).equals(pngSignature)) {
            console.log('✅ 生成的文件是有效的 PNG 格式');
          } else {
            console.log('⚠️ 生成的文件可能不是有效的 PNG 格式');
            console.log('文件头:', buffer.subarray(0, 16).toString('hex'));
          }
          
        } else {
          console.log('❌ toBuffer 方法不可用');
        }
      })
      .catch(error => {
        console.log('❌ 图表渲染失败:', error.message);
        console.log('错误详情:', error.stack);
      });
      
  } else {
    console.log('❌ render 函数不可用');
  }
  
} catch (error) {
  console.log('❌ 加载模块失败:', error.message);
  console.log('错误详情:', error.stack);
  
  // 检查具体的错误原因
  if (error.message.includes('canvas')) {
    console.log('\n🔍 Canvas 相关错误分析:');
    console.log('这通常是因为 canvas 模块没有正确编译');
    console.log('建议解决方案:');
    console.log('1. npm rebuild canvas');
    console.log('2. npm install canvas --build-from-source');
    console.log('3. 确保安装了必要的系统依赖');
  }
}

console.log('\n3. 测试完成');

// 创建一个简单的 HTTP 服务器用于测试
const http = require('http');

const server = http.createServer((req, res) => {
  if (req.url === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html>
<head>
    <title>GPT-Vis SSR 快速测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>GPT-Vis SSR 快速测试</h1>
    <div class="status success">
        <strong>Node.js 版本:</strong> ${process.version}
    </div>
    <div class="status success">
        <strong>服务器状态:</strong> 运行中
    </div>
    <p>请查看控制台输出了解详细的测试结果。</p>
    <p>如果测试成功，图片文件将保存在 test-output/test-chart.png</p>
</body>
</html>
    `);
  } else if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'ok',
      nodeVersion: process.version,
      timestamp: new Date().toISOString()
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
});

const PORT = 3003;
server.listen(PORT, () => {
  console.log(`\n🌐 测试服务器启动: http://localhost:${PORT}`);
  console.log('按 Ctrl+C 停止服务器');
});

process.on('SIGINT', () => {
  console.log('\n👋 关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
