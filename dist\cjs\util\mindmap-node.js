var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/util/mindmap-node.ts
var mindmap_node_exports = {};
__export(mindmap_node_exports, {
  MindmapNode: () => MindmapNode
});
module.exports = __toCommonJS(mindmap_node_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var { BaseNode } = import_g6_ssr.G6;
var MindmapNode = class extends BaseNode {
  getKeyStyle(attributes) {
    const [width, height] = this.getSize(attributes);
    const keyShape = super.getKeyStyle(attributes);
    return { width, height, ...keyShape };
  }
  drawKeyShape(attributes, container) {
    const keyStyle = this.getKeyStyle(attributes);
    return this.upsert("key", "rect", keyStyle, container);
  }
  render(attributes = this.parsedAttributes, container = this) {
    super.render(attributes, container);
  }
};
MindmapNode.defaultStyleProps = {
  ...BaseNode.defaultStyleProps,
  showIcon: false
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  MindmapNode
});
