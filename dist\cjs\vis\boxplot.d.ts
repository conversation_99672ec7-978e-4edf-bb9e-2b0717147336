import { CommonOptions } from './types';
type BoxplotDatum = {
    category: string;
    value: number;
    group?: string;
};
export type BoxplotOptions = CommonOptions & {
    /**
     * Title of the boxplot chart.
     */
    title?: string;
    /**
     * Data for the boxplot chart.
     */
    data: BoxplotDatum[];
    /**
     * axisYTitle of the boxplot chart.
     */
    axisYTitle?: string;
    /**
     * axisXTitle of the boxplot chart.
     */
    axisXTitle?: string;
};
export declare function Boxplot(options: BoxplotOptions): Promise<import("@antv/g2-ssr").Chart>;
export {};
