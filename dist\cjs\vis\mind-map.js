var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/mind-map.ts
var mind_map_exports = {};
__export(mind_map_exports, {
  MindMap: () => MindMap,
  treeToGraphData: () => treeToGraphData
});
module.exports = __toCommonJS(mind_map_exports);
var import_g6_ssr = require("@antv/g6-ssr");
var import_canvas = require("canvas");
var import_theme = require("../theme");
var import_util = require("../util");
var { register, idOf, positionOf, ExtensionCategory, treeToGraphData: treeToGraphDataG6 } = import_g6_ssr.G6;
register(ExtensionCategory.NODE, "mindmap", import_util.MindmapNode);
var MAX_WIDTH = 160;
var canvas = null;
var ctx = null;
var measureText = (style) => {
  if (!canvas) {
    canvas = (0, import_canvas.createCanvas)(0, 0);
    ctx = canvas.getContext("2d");
  }
  const font = [
    style.fontStyle || "normal",
    style.fontWeight || "normal",
    `${style.fontSize || 12}px`,
    style.fontFamily || "sans-serif"
  ].join(" ");
  ctx.font = font;
  const width = ctx.measureText(style.text).width;
  return width > MAX_WIDTH ? MAX_WIDTH : width;
};
var RootNodeStyle = {
  fill: "#EFF0F0",
  labelFill: "#262626",
  labelFontSize: 24,
  labelFontWeight: 600,
  labelOffsetY: 8,
  labelPlacement: "center",
  ports: [{ placement: "right" }, { placement: "left" }],
  radius: 8
};
var NodeStyle = {
  fill: "transparent",
  labelPlacement: "center",
  labelFontSize: 16,
  ports: [{ placement: "right-bottom" }, { placement: "left-bottom" }]
};
var getNodeWidth = (nodeId, isRoot) => {
  const padding = isRoot ? 40 : 30;
  const nodeStyle = isRoot ? RootNodeStyle : NodeStyle;
  return measureText({ text: nodeId, fontSize: nodeStyle.labelFontSize, fontFamily: "Gill Sans" }) + padding;
};
var getNodeSize = (nodeId, isRoot) => {
  const width = getNodeWidth(nodeId, isRoot);
  const height = isRoot ? 36 : 32;
  return [width, height];
};
var getNodeSide = (nodeData, parentData) => {
  if (!parentData)
    return "center";
  const nodePositionX = positionOf(nodeData)[0];
  const parentPositionX = positionOf(parentData)[0];
  return parentPositionX > nodePositionX ? "left" : "right";
};
function treeToGraphData(data) {
  return treeToGraphDataG6(data, {
    getNodeData: (datum, depth) => {
      datum.id = datum.name;
      datum.depth = depth;
      if (!datum.children)
        return datum;
      const { children, ...restDatum } = datum;
      return {
        ...restDatum,
        children: children.map((child) => child.name)
      };
    },
    getEdgeData: (source, target) => ({
      source: source.name,
      target: target.name
    })
  });
}
async function MindMap(options) {
  const { data, width = 600, height = 400, theme = "default" } = options;
  const dataParse = treeToGraphData(data);
  const rootId = data.name;
  return await (0, import_g6_ssr.createGraph)({
    waitForRender: 300,
    width,
    height,
    data: dataParse,
    devicePixelRatio: 3,
    padding: 24,
    autoFit: {
      type: "view"
    },
    node: {
      type: "mindmap",
      // @ts-ignore
      style: function(d) {
        var _a, _b, _c;
        const direction = getNodeSide(d, this.getParentData(idOf(d), "tree"));
        const isRoot = idOf(d) === rootId;
        const depth = d.depth;
        return {
          direction,
          labelText: idOf(d),
          size: getNodeSize(idOf(d), isRoot),
          labelFontFamily: "Gill Sans",
          lineWidth: 2,
          radius: 8,
          stroke: depth === 0 ? "#f1f4f5" : (_a = d.style) == null ? void 0 : _a.color,
          labelBackground: true,
          labelBackgroundFill: "transparent",
          labelPadding: direction === "left" ? [2, 0, 10, 40] : [2, 40, 10, 0],
          ...isRoot ? RootNodeStyle : NodeStyle,
          fill: depth === 0 ? "#f1f4f5" : depth === 1 ? (_b = d.style) == null ? void 0 : _b.color : "transparent",
          labelFill: depth === 0 ? "#262626" : depth === 1 ? "#FFF" : (_c = d.style) == null ? void 0 : _c.color,
          ports: [{ placement: "left" }, { placement: "right" }],
          labelMaxWidth: MAX_WIDTH + 20,
          labelTextOverflow: "ellipsis",
          labelWordWrap: true,
          labelMaxLines: 1
        };
      }
    },
    edge: {
      type: "cubic-horizontal",
      style: {
        lineWidth: 1.5,
        // @ts-ignore
        stroke: function(data2) {
          return this.getNodeData(data2.target).depth > 1 ? (
            // @ts-ignore
            this.getNodeData(data2.target).style.color
          ) : "#99ADD1";
        }
      }
    },
    layout: {
      type: "mindmap",
      direction: "H",
      getHeight: () => 30,
      getWidth: (node) => getNodeWidth(node.id, node.id === rootId),
      getVGap: () => 14,
      getHGap: () => 64,
      animation: false
    },
    transforms: [import_theme.G6THEME_MAP[theme]],
    animation: false
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  MindMap,
  treeToGraphData
});
