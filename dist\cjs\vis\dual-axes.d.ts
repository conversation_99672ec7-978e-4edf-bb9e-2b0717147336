import { CommonOptions } from './types';
type DualAxesSeriesItem = {
    type: string;
    data: number[];
    axisYTitle?: string;
};
export type DualAxesOptions = CommonOptions & {
    title?: string;
    categories: string[];
    series: DualAxesSeriesItem[];
    axisXTitle?: string;
    legendTypeList?: string[];
};
export declare function DualAxes(options: DualAxesOptions): Promise<import("@antv/g2-ssr").Chart>;
export {};
