/**
 * GPT-Vis SSR API 使用示例
 * 这个文件展示了如何在实际项目中集成和使用 gpt-vis-ssr
 */

const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');

// 注意：由于 canvas 模块编译问题，这里使用模拟的 render 函数
// 在实际部署中，请确保 canvas 模块正确编译后使用真实的 render 函数

// 模拟的 render 函数（用于演示）
async function mockRender(options) {
  console.log('模拟渲染图表:', options.type);
  
  // 模拟异步处理时间
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // 返回模拟的结果对象
  return {
    toBuffer: () => {
      // 返回一个简单的 PNG 头部作为示例
      return Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG 签名
        0x00, 0x00, 0x00, 0x0D, // IHDR 长度
        0x49, 0x48, 0x44, 0x52, // IHDR
        0x00, 0x00, 0x00, 0x64, // 宽度 100
        0x00, 0x00, 0x00, 0x64, // 高度 100
        0x08, 0x02, 0x00, 0x00, 0x00, // 位深度、颜色类型等
        0x4C, 0x5C, 0x6D, 0x7E  // CRC
      ]);
    },
    exportToFile: (filename) => {
      const buffer = this.toBuffer();
      fs.writeFileSync(filename, buffer);
    },
    toDataURL: () => {
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    }
  };
}

// 在实际部署中，取消注释下面这行来使用真实的 render 函数
// const { render } = require('./dist/cjs/index.js');
const render = mockRender; // 使用模拟函数

// 图表配置模板
const chartTemplates = {
  line: {
    type: 'line',
    data: [
      { time: '2020', value: 100 },
      { time: '2021', value: 120 },
      { time: '2022', value: 150 },
      { time: '2023', value: 180 },
      { time: '2024', value: 200 }
    ],
    width: 600,
    height: 400,
    axisXTitle: 'Year',
    axisYTitle: 'Value'
  },
  bar: {
    type: 'bar',
    data: [
      { category: 'A', value: 30 },
      { category: 'B', value: 45 },
      { category: 'C', value: 25 },
      { category: 'D', value: 60 }
    ],
    width: 600,
    height: 400
  },
  pie: {
    type: 'pie',
    data: [
      { category: 'Desktop', value: 60 },
      { category: 'Mobile', value: 30 },
      { category: 'Tablet', value: 10 }
    ],
    width: 400,
    height: 400
  }
};

// 创建 API 服务器
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    if (pathname === '/api/chart' && method === 'POST') {
      // 生成图表 API
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', async () => {
        try {
          const options = JSON.parse(body);
          
          // 验证必需参数
          if (!options.type) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: '缺少图表类型参数' }));
            return;
          }

          console.log(`生成图表请求: ${options.type}`);
          
          // 渲染图表
          const vis = await render(options);
          const buffer = vis.toBuffer();

          // 返回图片
          res.writeHead(200, { 
            'Content-Type': 'image/png',
            'Content-Length': buffer.length
          });
          res.end(buffer);

        } catch (error) {
          console.error('图表生成错误:', error);
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: '图表生成失败: ' + error.message }));
        }
      });

    } else if (pathname === '/api/templates' && method === 'GET') {
      // 获取图表模板 API
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        templates: Object.keys(chartTemplates),
        data: chartTemplates
      }));

    } else if (pathname.startsWith('/api/template/') && method === 'GET') {
      // 获取特定模板并生成图表
      const templateName = pathname.split('/')[3];
      
      if (!chartTemplates[templateName]) {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: '模板不存在' }));
        return;
      }

      const template = chartTemplates[templateName];
      console.log(`使用模板生成图表: ${templateName}`);
      
      const vis = await render(template);
      const buffer = vis.toBuffer();

      res.writeHead(200, { 
        'Content-Type': 'image/png',
        'Content-Length': buffer.length
      });
      res.end(buffer);

    } else if (pathname === '/api/health' && method === 'GET') {
      // 健康检查 API
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'healthy',
        service: 'gpt-vis-ssr-api',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version
      }));

    } else if (pathname === '/api/docs' && method === 'GET') {
      // API 文档
      const docs = {
        title: 'GPT-Vis SSR API 文档',
        version: '1.0.0',
        endpoints: [
          {
            path: '/api/chart',
            method: 'POST',
            description: '生成自定义图表',
            body: {
              type: 'string (必需) - 图表类型',
              data: 'array (必需) - 图表数据',
              width: 'number (可选) - 图表宽度',
              height: 'number (可选) - 图表高度',
              '...': '其他图表配置选项'
            },
            response: 'image/png'
          },
          {
            path: '/api/templates',
            method: 'GET',
            description: '获取所有可用的图表模板',
            response: 'application/json'
          },
          {
            path: '/api/template/{name}',
            method: 'GET',
            description: '使用指定模板生成图表',
            response: 'image/png'
          },
          {
            path: '/api/health',
            method: 'GET',
            description: '服务健康检查',
            response: 'application/json'
          }
        ],
        examples: {
          'POST /api/chart': {
            type: 'line',
            data: [
              { time: '2020', value: 100 },
              { time: '2021', value: 120 }
            ],
            width: 600,
            height: 400
          }
        }
      };

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(docs, null, 2));

    } else {
      // 404
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: '接口不存在' }));
    }

  } catch (error) {
    console.error('服务器错误:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: '内部服务器错误' }));
  }
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 GPT-Vis SSR API 服务器已启动！`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`📚 API 文档: http://localhost:${PORT}/api/docs`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/api/health`);
  console.log(`📊 模板列表: http://localhost:${PORT}/api/templates`);
  console.log(`\n示例请求:`);
  console.log(`curl -X POST http://localhost:${PORT}/api/chart \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -d '{"type":"line","data":[{"time":"2020","value":100}]}' \\`);
  console.log(`  --output chart.png`);
  console.log(`\n✨ 服务器正在运行，按 Ctrl+C 停止服务`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭 API 服务器...');
  server.close(() => {
    console.log('✅ API 服务器已关闭');
    process.exit(0);
  });
});

module.exports = server;
