var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/radar.ts
var radar_exports = {};
__export(radar_exports, {
  Radar: () => Radar
});
module.exports = __toCommonJS(radar_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
var import_util = require("../util");
function transformRadartoParallel(data) {
  if (!data || data.length === 0) {
    return [];
  }
  const groups = (0, import_util.groupBy)(data, (d) => d.group || "");
  return Object.entries(groups).map(([group, values]) => {
    const paralleValues = (values || []).reduce((acc, { name, value }) => {
      acc[name] = value;
      return acc;
    }, {});
    return {
      ...paralleValues,
      group
    };
  });
}
async function Radar(options) {
  const { data, title, width = 600, height = 400, theme = "default" } = options;
  const parallelData = transformRadartoParallel(data);
  const position = Object.keys(parallelData[0] || {}).filter((key) => key !== "group");
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    title,
    theme: import_theme.THEME_MAP[theme],
    width,
    height,
    inset: 18,
    type: "line",
    data: parallelData,
    coordinate: { type: "radar" },
    encode: {
      position,
      color: "group"
    },
    style: { lineWidth: 2, lineCap: "round", lineJoin: "round" },
    legend: {
      color: parallelData.length > 1 ? { itemMarker: "point" } : false
    },
    scale: Object.fromEntries(
      Array.from({ length: position.length }, (_, i) => [
        `position${i === 0 ? "" : i}`,
        {
          domainMin: 0,
          nice: true
        }
      ])
    ),
    axis: Object.fromEntries(
      Array.from({ length: position.length }, (_, i) => {
        return [
          `position${i === 0 ? "" : i}`,
          {
            zIndex: 1,
            titleFontSize: 10,
            titleSpacing: 8,
            label: true,
            labelFill: "#000",
            labelOpacity: 0.45,
            labelFontSize: 10,
            line: true,
            lineFill: "#000",
            lineStrokeOpacity: 0.25,
            tickFilter: (_2, idx) => {
              return !(i !== 0 && idx === 0);
            },
            tickCount: 4,
            gridStrokeOpacity: 0.45,
            gridStroke: "#000",
            gridLineWidth: 1,
            gridLineDash: [4, 4]
          }
        ];
      })
    ),
    interaction: { tooltip: false }
    // TODO: area and point area not supported in radar chart yet
    // children: [
    //   {
    //     type: 'area',
    //     style: { fillOpacity: 0.4 },
    //   },
    //   {
    //     type: 'line',
    //     style: { lineWidth: 2 },
    //   },
    //   {
    //     type: 'point',
    //     encode: { shape: 'point' },
    //     style: { fill: 'white', lineWidth: 1 },
    //   },
    // ],
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Radar
});
