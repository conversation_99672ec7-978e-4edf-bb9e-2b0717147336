import { G6 } from '@antv/g6-ssr';
import { ACADEMY_COLOR_PALETTE, AssignColorByBranchTransform, DEFAULT_COLOR_PALETTE } from "./util";
var register = G6.register,
  ExtensionCategory = G6.ExtensionCategory;
register(ExtensionCategory.TRANSFORM, 'assign-color-by-branch', AssignColorByBranchTransform);
var DEFAULT_THEME = {
  type: 'light',
  view: {
    viewFill: '#FFF',
    plotFill: 'transparent',
    mainFill: 'transparent',
    contentFill: 'transparent'
  },
  interval: {
    rect: {
      fillOpacity: 0.8
    }
  },
  line: {
    line: {
      lineWidth: 2
    }
  },
  area: {
    area: {
      fillOpacity: 0.6
    }
  },
  point: {
    point: {
      lineWidth: 1
    }
  }
};
var ACADEMY_THEME = {
  type: 'academy',
  view: {
    viewFill: '#FFF',
    plotFill: 'transparent',
    mainFill: 'transparent',
    contentFill: 'transparent'
  },
  interval: {
    rect: {
      fillOpacity: 0.8
    }
  },
  line: {
    line: {
      lineWidth: 2
    }
  },
  area: {
    area: {
      fillOpacity: 0.6
    }
  },
  point: {
    point: {
      lineWidth: 1
    }
  }
};
export var THEME_MAP = {
  default: DEFAULT_THEME,
  academy: ACADEMY_THEME
};
export var G6THEME_MAP = {
  default: {
    type: 'assign-color-by-branch',
    colors: DEFAULT_COLOR_PALETTE
  },
  academy: {
    type: 'assign-color-by-branch',
    colors: ACADEMY_COLOR_PALETTE
  }
};