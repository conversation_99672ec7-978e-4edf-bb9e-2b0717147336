import { CommonOptions } from './types';
type ViolinDatum = {
    category: string;
    value: number;
    group?: string;
};
export type ViolinOptions = CommonOptions & {
    /**
     * Title of the violin chart.
     */
    title?: string;
    /**
     * Data for the violin chart.
     */
    data: ViolinDatum[];
    /**
     * axisYTitle of the violin chart.
     */
    axisYTitle?: string;
    /**
     * axisXTitle of the violin chart.
     */
    axisXTitle?: string;
};
export declare function Violin(options: ViolinOptions): Promise<import("@antv/g2-ssr").Chart>;
export {};
