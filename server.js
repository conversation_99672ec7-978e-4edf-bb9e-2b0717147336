const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');

// 创建一个简单的 HTTP 服务器来演示 gpt-vis-ssr 的功能
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  // 设置 CORS 头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    if (pathname === '/') {
      // 返回主页面
      const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT-Vis SSR 本地部署演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .chart-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .chart-card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 20px;
            background: #fafafa;
        }
        .chart-card h3 {
            margin-top: 0;
            color: #262626;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .code {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 GPT-Vis SSR 本地部署成功！</h1>
        
        <div class="status success">
            <strong>✅ 部署状态：</strong> 服务器运行正常
        </div>
        
        <div class="status info">
            <strong>📋 项目信息：</strong><br>
            • 项目名称：@antv/gpt-vis-ssr<br>
            • 版本：0.1.6<br>
            • 描述：AntV GPT-Vis 的服务器端渲染工具<br>
            • Node.js 版本要求：>=18<br>
            • 当前运行端口：3000
        </div>

        <div class="status info">
            <strong>🔧 使用方法：</strong><br>
            <div class="code">
const { render } = require('@antv/gpt-vis-ssr');

const options = {
  type: 'line',
  data: [
    { time: 2018, value: 91.9 },
    { time: 2019, value: 99.1 },
    { time: 2020, value: 101.6 },
    { time: 2021, value: 114.4 },
    { time: 2022, value: 121 },
  ],
};

const vis = await render(options);
const buffer = vis.toBuffer();
            </div>
        </div>

        <h2>📊 支持的图表类型</h2>
        <div class="chart-examples">
            <div class="chart-card">
                <h3>基础图表</h3>
                <ul>
                    <li>线图 (line)</li>
                    <li>柱状图 (bar)</li>
                    <li>列图 (column)</li>
                    <li>面积图 (area)</li>
                    <li>散点图 (scatter)</li>
                    <li>饼图 (pie)</li>
                </ul>
            </div>
            <div class="chart-card">
                <h3>统计图表</h3>
                <ul>
                    <li>箱线图 (boxplot)</li>
                    <li>直方图 (histogram)</li>
                    <li>小提琴图 (violin)</li>
                    <li>雷达图 (radar)</li>
                    <li>双轴图 (dual-axes)</li>
                    <li>韦恩图 (venn)</li>
                </ul>
            </div>
            <div class="chart-card">
                <h3>特殊图表</h3>
                <ul>
                    <li>漏斗图 (funnel)</li>
                    <li>桑基图 (sankey)</li>
                    <li>树图 (treemap)</li>
                    <li>水波图 (liquid)</li>
                    <li>词云 (word-cloud)</li>
                </ul>
            </div>
            <div class="chart-card">
                <h3>关系图表</h3>
                <ul>
                    <li>网络图 (network-graph)</li>
                    <li>流程图 (flow-diagram)</li>
                    <li>思维导图 (mind-map)</li>
                    <li>组织架构图 (organization-chart)</li>
                    <li>鱼骨图 (fishbone-diagram)</li>
                </ul>
            </div>
        </div>

        <h2>🚀 下一步</h2>
        <div class="status info">
            <strong>要开始使用 GPT-Vis SSR，您可以：</strong><br>
            1. 在您的 Node.js 项目中安装：<code>npm install @antv/gpt-vis-ssr</code><br>
            2. 导入并使用 render 函数生成图表<br>
            3. 将生成的 Buffer 保存为图片文件或返回给客户端<br>
            4. 查看项目文档：<a href="https://gpt-vis.antv.vision" target="_blank">https://gpt-vis.antv.vision</a>
        </div>
    </div>
</body>
</html>`;
      
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(html);
      
    } else if (pathname === '/health') {
      // 健康检查端点
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'ok',
        service: 'gpt-vis-ssr',
        version: '0.1.6',
        timestamp: new Date().toISOString(),
        node_version: process.version
      }));
      
    } else {
      // 404 页面
      res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <html>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1>404 - 页面未找到</h1>
            <p>请访问 <a href="/">主页</a></p>
          </body>
        </html>
      `);
    }
    
  } catch (error) {
    console.error('服务器错误:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      error: '内部服务器错误',
      message: error.message
    }));
  }
});

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log(`🚀 GPT-Vis SSR 服务器已启动！`);
  console.log(`📍 访问地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📚 项目文档: https://gpt-vis.antv.vision`);
  console.log(`\n✨ 服务器正在运行，按 Ctrl+C 停止服务`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n👋 收到终止信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
