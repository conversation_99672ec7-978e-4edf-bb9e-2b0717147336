function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _get() { if (typeof Reflect !== "undefined" && Reflect.get) { _get = Reflect.get.bind(); } else { _get = function _get(target, property, receiver) { var base = _superPropBase(target, property); if (!base) return; var desc = Object.getOwnPropertyDescriptor(base, property); if (desc.get) { return desc.get.call(arguments.length < 3 ? target : receiver); } return desc.value; }; } return _get.apply(this, arguments); }
function _superPropBase(object, property) { while (!Object.prototype.hasOwnProperty.call(object, property)) { object = _getPrototypeOf(object); if (object === null) break; } return object; }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }
import { G6 } from '@antv/g6-ssr';
var Rect = G6.Rect,
  Label = G6.Label,
  Badge = G6.Badge;

/**
 * Custom node for OrganizationChart visualization.
 */
export var OrganizationChartNode = /*#__PURE__*/function (_Rect) {
  _inherits(OrganizationChartNode, _Rect);
  var _super = _createSuper(OrganizationChartNode);
  function OrganizationChartNode() {
    _classCallCheck(this, OrganizationChartNode);
    return _super.apply(this, arguments);
  }
  _createClass(OrganizationChartNode, [{
    key: "data",
    get: function get() {
      return this.context.model.getElementDataById(this.id).data;
    }
  }, {
    key: "getLabelStyle",
    value: function getLabelStyle() {
      var text = this.data.name;
      return {
        text: text,
        fill: '#2078B4',
        fontSize: 16,
        fontWeight: 400,
        textAlign: 'left',
        transform: [['translate', -55, -10]]
      };
    }
  }, {
    key: "getKeyStyle",
    value: function getKeyStyle(attributes) {
      return _objectSpread(_objectSpread({}, _get(_getPrototypeOf(OrganizationChartNode.prototype), "getKeyStyle", this).call(this, attributes)), {}, {
        fill: '#fff'
      });
    }

    // Draws the description shape.
  }, {
    key: "drawDescriptionShape",
    value: function drawDescriptionShape(attributes, container) {
      var positionStyle = {
        text: this.data.description || '-',
        // Default text if no description is provided
        fontSize: 14,
        fill: '#343f4a',
        textAlign: 'left',
        fontStyle: 'italic',
        transform: [['translate', -55, 10]],
        wordWrapWidth: 200 - 32 - 16 // Width of the node (200) minus the width of the status icon (32) and some padding (16)
      };
      this.upsert('position', Label, positionStyle, container);
    }

    // Draws the organization icon shape.
  }, {
    key: "drawOrganizationIconShape",
    value: function drawOrganizationIconShape(attributes, container) {
      var _this$data;
      var _attributes$fill = attributes.fill,
        fill = _attributes$fill === void 0 ? '#1783FF' : _attributes$fill;
      var iconStyle = {
        text: (((_this$data = this.data) === null || _this$data === void 0 ? void 0 : _this$data.name) || 'V').slice(0, 1),
        fontSize: 18,
        textAlign: 'center',
        // Half of the width of the node (200) minus half of the status icon width (32) plus some padding (4)
        transform: [['translate', -200 / 2 + 32 / 2 + 8, 0]],
        fill: '#000',
        backgroundFill: fill,
        backgroundRadius: 4,
        backgroundWidth: 32,
        backgroundHeight: 32,
        backgroundOpacity: 0.5
      };
      this.upsert('organization-icon', Badge, iconStyle, container);
    }
  }, {
    key: "render",
    value: function render() {
      var attributes = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.parsedAttributes;
      var container = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this;
      _get(_getPrototypeOf(OrganizationChartNode.prototype), "render", this).call(this, attributes, container);
      this.drawDescriptionShape(attributes, container);
      this.drawOrganizationIconShape(attributes, container);
    }
  }]);
  return OrganizationChartNode;
}(Rect);