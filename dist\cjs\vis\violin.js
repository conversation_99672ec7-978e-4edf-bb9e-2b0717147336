var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/violin.ts
var violin_exports = {};
__export(violin_exports, {
  Violin: () => Violin
});
module.exports = __toCommonJS(violin_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Violin(options) {
  var _a;
  const {
    data,
    title,
    width = 600,
    height = 400,
    axisYTitle,
    axisXTitle,
    theme = "default"
  } = options;
  const hasGroupField = ((_a = (data || [])[0]) == null ? void 0 : _a.group) !== void 0;
  let encode = {};
  let children = [];
  if (hasGroupField) {
    encode = {
      x: "category",
      y: "y",
      color: "group",
      series: "group",
      size: "size"
    };
    children = [
      {
        type: "density",
        data: {
          transform: [{ type: "kde", field: "value", groupBy: ["category", "group"] }]
        },
        encode
      },
      {
        type: "boxplot",
        encode: {
          x: "category",
          y: "value",
          series: "group",
          color: "group",
          shape: "violin"
        },
        style: { opacity: 0.5, strokeOpacity: 0.5, point: false }
      }
    ];
  } else {
    encode = {
      x: "category",
      y: "y",
      color: "category",
      size: "size"
    };
    children = [
      {
        type: "density",
        data: {
          transform: [{ type: "kde", field: "value", groupBy: ["category"], size: 20 }]
        },
        encode
      },
      {
        type: "boxplot",
        encode: {
          x: "category",
          y: "value",
          color: "category",
          shape: "violin"
        },
        style: { opacity: 0.5, strokeOpacity: 0.5, point: false }
      }
    ];
  }
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "view",
    theme: import_theme.THEME_MAP[theme],
    width,
    height,
    title,
    autoFit: true,
    data,
    axis: {
      y: {
        title: axisYTitle || false
      },
      x: {
        title: axisXTitle || false
      }
    },
    children
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Violin
});
