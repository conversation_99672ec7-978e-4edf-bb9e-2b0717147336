var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/funnel.ts
var funnel_exports = {};
__export(funnel_exports, {
  Funnel: () => Funnel
});
module.exports = __toCommonJS(funnel_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Funnel(options) {
  const { data, title, width = 600, height = 400, theme = "default" } = options;
  const r = (start, end) => `${((start - end) / start * 100).toFixed(2)} %`;
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "view",
    width,
    height,
    data,
    theme: import_theme.THEME_MAP[theme],
    title,
    padding: 40,
    children: [
      {
        type: "interval",
        data,
        encode: { x: "category", y: "value", color: "category", shape: "funnel" },
        transform: [{ type: "symmetryY" }],
        scale: { x: { padding: 0 } },
        coordinate: { transform: [{ type: "transpose" }] },
        legend: {
          color: {
            position: "top",
            layout: {
              justifyContent: "center"
            }
          }
        },
        labels: [
          {
            text: (d) => `${d.category}
${d.value}`,
            position: "inside",
            transform: [{ type: "contrastReverse" }]
          },
          {
            text: (d, i) => i !== 0 ? "———" : "",
            style: {
              "font-size": "1px",
              color: "#666",
              "letter-spacing": "0px"
            },
            position: "top-right",
            fill: "#666",
            dx: 35,
            dy: -8
          },
          {
            text: (d, i) => i !== 0 ? "转换率" : "",
            position: "top-right",
            textAlign: "left",
            textBaseline: "middle",
            fill: "#666",
            dx: 40
          },
          {
            text: (d, i, data2) => i !== 0 ? r(data2[i - 1].value, data2[i].value) : "",
            position: "top-right",
            textAlign: "left",
            textBaseline: "middle",
            dx: 80
          }
        ]
      },
      {
        type: "connector",
        data: [
          {
            startX: data[0].category,
            startY: data[data.length - 1].category,
            endX: 0,
            endY: (data[0].value - data[data.length - 1].value) / 2
          }
        ],
        encode: { x: "startX", x1: "startY", y: "endX", y1: "endY" },
        style: {
          stroke: "#666",
          markerEnd: false,
          connectLength1: -12,
          offset2: -20,
          connectorStroke: "#0649f2",
          lineDash: [12, 2]
        },
        labels: [
          {
            text: "转换率",
            position: "left",
            textAlign: "start",
            textBaseline: "middle",
            fill: "#666",
            dx: 10
          },
          {
            text: r(data[0].value, data[data.length - 1].value),
            position: "left",
            textAlign: "start",
            dx: 50,
            fill: "#000"
          }
        ]
      }
    ],
    axis: false,
    animate: false
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Funnel
});
