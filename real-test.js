const { render } = require('./dist/cjs/index.js');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始真实的 GPT-Vis SSR 测试...');
console.log('Node.js 版本:', process.version);

async function testRealRendering() {
  try {
    // 创建输出目录
    const outputDir = './real-output';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir);
    }

    console.log('\n📊 测试 1: 线图渲染...');
    const lineOptions = {
      type: 'line',
      data: [
        { time: '2020', value: 100 },
        { time: '2021', value: 120 },
        { time: '2022', value: 150 },
        { time: '2023', value: 180 },
        { time: '2024', value: 200 }
      ],
      width: 600,
      height: 400,
      axisXTitle: 'Year',
      axisYTitle: 'Value'
    };

    const lineVis = await render(lineOptions);
    const lineBuffer = lineVis.toBuffer();
    fs.writeFileSync(path.join(outputDir, 'real-line-chart.png'), lineBuffer);
    console.log('✅ 线图渲染成功，文件大小:', lineBuffer.length, 'bytes');

    console.log('\n📊 测试 2: 柱状图渲染...');
    const barOptions = {
      type: 'bar',
      data: [
        { category: 'Product A', value: 30 },
        { category: 'Product B', value: 45 },
        { category: 'Product C', value: 25 },
        { category: 'Product D', value: 60 },
        { category: 'Product E', value: 35 }
      ],
      width: 600,
      height: 400,
      axisXTitle: 'Products',
      axisYTitle: 'Sales'
    };

    const barVis = await render(barOptions);
    const barBuffer = barVis.toBuffer();
    fs.writeFileSync(path.join(outputDir, 'real-bar-chart.png'), barBuffer);
    console.log('✅ 柱状图渲染成功，文件大小:', barBuffer.length, 'bytes');

    console.log('\n📊 测试 3: 饼图渲染...');
    const pieOptions = {
      type: 'pie',
      data: [
        { category: 'Desktop', value: 60 },
        { category: 'Mobile', value: 30 },
        { category: 'Tablet', value: 10 }
      ],
      width: 400,
      height: 400
    };

    const pieVis = await render(pieOptions);
    const pieBuffer = pieVis.toBuffer();
    fs.writeFileSync(path.join(outputDir, 'real-pie-chart.png'), pieBuffer);
    console.log('✅ 饼图渲染成功，文件大小:', pieBuffer.length, 'bytes');

    console.log('\n📊 测试 4: 面积图渲染...');
    const areaOptions = {
      type: 'area',
      data: [
        { time: 'Jan', value: 100 },
        { time: 'Feb', value: 120 },
        { time: 'Mar', value: 150 },
        { time: 'Apr', value: 180 },
        { time: 'May', value: 200 },
        { time: 'Jun', value: 170 }
      ],
      width: 600,
      height: 400
    };

    const areaVis = await render(areaOptions);
    const areaBuffer = areaVis.toBuffer();
    fs.writeFileSync(path.join(outputDir, 'real-area-chart.png'), areaBuffer);
    console.log('✅ 面积图渲染成功，文件大小:', areaBuffer.length, 'bytes');

    console.log('\n📊 测试 5: 散点图渲染...');
    const scatterOptions = {
      type: 'scatter',
      data: [
        { x: 10, y: 20 },
        { x: 20, y: 30 },
        { x: 30, y: 25 },
        { x: 40, y: 35 },
        { x: 50, y: 45 },
        { x: 60, y: 40 }
      ],
      width: 600,
      height: 400
    };

    const scatterVis = await render(scatterOptions);
    const scatterBuffer = scatterVis.toBuffer();
    fs.writeFileSync(path.join(outputDir, 'real-scatter-chart.png'), scatterBuffer);
    console.log('✅ 散点图渲染成功，文件大小:', scatterBuffer.length, 'bytes');

    // 验证生成的文件
    console.log('\n🔍 验证生成的文件...');
    const files = fs.readdirSync(outputDir);
    files.forEach(file => {
      const filePath = path.join(outputDir, file);
      const stats = fs.statSync(filePath);
      const buffer = fs.readFileSync(filePath);
      
      // 检查 PNG 文件头
      const pngSignature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
      const isValidPNG = buffer.subarray(0, 8).equals(pngSignature);
      
      console.log(`📄 ${file}:`);
      console.log(`   大小: ${stats.size} bytes`);
      console.log(`   PNG 格式: ${isValidPNG ? '✅ 有效' : '❌ 无效'}`);
      console.log(`   修改时间: ${stats.mtime.toLocaleString('zh-CN')}`);
    });

    console.log('\n🎉 所有测试完成！真实图表渲染功能正常工作！');
    console.log(`📁 生成的图表文件保存在: ${path.resolve(outputDir)}`);
    
    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error.stack);
    return false;
  }
}

// 运行测试
testRealRendering().then(success => {
  if (success) {
    console.log('\n✨ 真实渲染测试成功完成！');
  } else {
    console.log('\n💥 真实渲染测试失败！');
  }
});
