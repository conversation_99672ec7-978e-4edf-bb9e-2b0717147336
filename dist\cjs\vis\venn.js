var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/venn.ts
var venn_exports = {};
__export(venn_exports, {
  Venn: () => Venn
});
module.exports = __toCommonJS(venn_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Venn(options) {
  const { data, title, width = 600, height = 400, theme = "default" } = options;
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "path",
    theme: import_theme.THEME_MAP[theme],
    title,
    width,
    height,
    data: {
      type: "inline",
      value: data,
      transform: [
        {
          type: "venn",
          padding: 8,
          sets: "sets",
          size: "value",
          as: ["key", "path"]
        }
      ]
    },
    encode: { d: "path", color: "key" },
    style: {
      opacity: (d) => d.sets.length > 1 ? 1e-3 : 0.65
    },
    labels: [
      {
        position: "inside",
        text: (d) => d.label || "",
        // transform: [{ type: 'contrastReverse' }],
        fill: "#000",
        fillOpacity: 0.85,
        fontSize: 10
      }
    ],
    legend: false,
    axis: false,
    tooltip: false,
    animate: false
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Venn
});
