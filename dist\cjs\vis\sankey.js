var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/sankey.ts
var sankey_exports = {};
__export(sankey_exports, {
  Sankey: () => Sankey
});
module.exports = __toCommonJS(sankey_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
var import_util = require("../util");
async function Sankey(options) {
  const {
    title,
    width = 600,
    height = 400,
    theme = "default",
    data,
    nodeAlign = "center"
  } = options;
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    title,
    width,
    height,
    theme: import_theme.THEME_MAP[theme],
    type: "sankey",
    layout: {
      nodeAlign,
      nodePadding: 0.01
    },
    data: {
      type: "inline",
      value: data,
      transform: [
        {
          type: "custom",
          callback: (data2) => ({
            links: data2
          })
        }
      ]
    },
    scale: {
      color: { range: theme === "academy" ? import_util.ACADEMY_COLOR_PALETTE : import_util.DEFAULT_COLOR_PALETTE }
    },
    style: {
      labelSpacing: 2,
      nodeLineWidth: 1,
      linkFillOpacity: 0.3
    },
    animate: false,
    tooltip: false,
    legend: false
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Sankey
});
