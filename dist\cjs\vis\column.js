var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/column.ts
var column_exports = {};
__export(column_exports, {
  Column: () => Column
});
module.exports = __toCommonJS(column_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Column(options) {
  var _a;
  const {
    data,
    title,
    width = 600,
    height = 400,
    axisYTitle,
    axisXTitle,
    group,
    stack,
    theme = "default"
  } = options;
  const hasGroupField = ((_a = (data || [])[0]) == null ? void 0 : _a.group) !== void 0;
  let transforms = [];
  let radiusStyle = {};
  let encode = {};
  if (theme === "default") {
    radiusStyle = { radiusTopLeft: 4, radiusTopRight: 4 };
  }
  if (group) {
    transforms = [
      {
        type: "dodgeX"
      }
    ];
  }
  if (stack) {
    transforms = [
      {
        type: "stackY"
      }
    ];
  }
  if (hasGroupField) {
    encode = {
      x: "category",
      y: "value",
      color: "group"
    };
  } else {
    encode = {
      x: "category",
      y: "value",
      color: "category"
    };
  }
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    theme: import_theme.THEME_MAP[theme],
    width,
    height,
    title,
    data,
    type: "interval",
    encode,
    transform: transforms,
    insetRight: 12,
    style: {
      ...radiusStyle,
      columnWidthRatio: 0.8
    },
    axis: {
      x: {
        title: axisXTitle
      },
      y: {
        title: axisYTitle
      }
    }
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Column
});
