const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🚀 启动简化测试服务器...');
console.log('Node.js 版本:', process.version);

// 尝试加载 gpt-vis-ssr
let render;
let renderStatus = 'unknown';

try {
  console.log('尝试加载 gpt-vis-ssr...');
  const gptVis = require('./dist/cjs/index.js');
  render = gptVis.render;
  renderStatus = 'real';
  console.log('✅ 成功加载真实的 gpt-vis-ssr 渲染引擎');
} catch (error) {
  console.log('❌ 加载 gpt-vis-ssr 失败:', error.message);
  console.log('使用模拟渲染引擎...');
  renderStatus = 'mock';
  
  // 创建一个有效的 PNG 图片
  function createValidPNG(width = 400, height = 300, color = [255, 255, 255]) {
    const PNG = require('pngjs').PNG;
    const png = new PNG({ width, height });
    
    // 填充白色背景
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (width * y + x) << 2;
        png.data[idx] = color[0];     // Red
        png.data[idx + 1] = color[1]; // Green
        png.data[idx + 2] = color[2]; // Blue
        png.data[idx + 3] = 255;      // Alpha
      }
    }
    
    return PNG.sync.write(png);
  }
  
  render = async function(options) {
    console.log(`模拟渲染 ${options.type} 图表...`);
    
    const width = options.width || 600;
    const height = options.height || 400;
    
    // 根据图表类型使用不同颜色
    const colors = {
      line: [100, 150, 255],
      bar: [255, 150, 100],
      pie: [150, 255, 100],
      area: [255, 200, 150],
      scatter: [200, 100, 255]
    };
    
    const color = colors[options.type] || [200, 200, 200];
    const buffer = createValidPNG(width, height, color);
    
    return {
      toBuffer: () => buffer,
      exportToFile: (filename) => {
        fs.writeFileSync(filename, buffer);
      },
      toDataURL: () => {
        return 'data:image/png;base64,' + buffer.toString('base64');
      }
    };
  };
}

// 创建 HTTP 服务器
const server = http.createServer(async (req, res) => {
  const url = require('url').parse(req.url, true);
  
  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  try {
    if (url.pathname === '/') {
      // 主页
      const html = `
<!DOCTYPE html>
<html>
<head>
    <title>GPT-Vis SSR 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .test-links { margin: 20px 0; }
        .test-links a { display: inline-block; margin: 5px; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>GPT-Vis SSR 测试服务器</h1>
    <div class="status ${renderStatus === 'real' ? 'success' : 'warning'}">
        <strong>渲染引擎状态:</strong> ${renderStatus === 'real' ? '✅ 真实渲染引擎' : '⚠️ 模拟渲染引擎'}
    </div>
    <div class="status success">
        <strong>Node.js 版本:</strong> ${process.version}
    </div>
    
    <h2>测试图表生成</h2>
    <div class="test-links">
        <a href="/chart/line" target="_blank">线图</a>
        <a href="/chart/bar" target="_blank">柱状图</a>
        <a href="/chart/pie" target="_blank">饼图</a>
        <a href="/chart/area" target="_blank">面积图</a>
        <a href="/chart/scatter" target="_blank">散点图</a>
    </div>
    
    <h2>API 测试</h2>
    <div class="test-links">
        <a href="/api/health" target="_blank">健康检查</a>
        <a href="/api/status" target="_blank">状态信息</a>
    </div>
</body>
</html>`;
      
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(html);
      
    } else if (url.pathname.startsWith('/chart/')) {
      // 生成图表
      const chartType = url.pathname.split('/')[2];
      
      const sampleData = {
        line: [
          { time: '2020', value: 100 },
          { time: '2021', value: 120 },
          { time: '2022', value: 150 },
          { time: '2023', value: 180 }
        ],
        bar: [
          { category: 'A', value: 30 },
          { category: 'B', value: 45 },
          { category: 'C', value: 25 }
        ],
        pie: [
          { category: 'Desktop', value: 60 },
          { category: 'Mobile', value: 30 },
          { category: 'Tablet', value: 10 }
        ],
        area: [
          { time: '2020', value: 100 },
          { time: '2021', value: 120 },
          { time: '2022', value: 150 }
        ],
        scatter: [
          { x: 10, y: 20 },
          { x: 20, y: 30 },
          { x: 30, y: 25 }
        ]
      };
      
      const options = {
        type: chartType,
        data: sampleData[chartType] || sampleData.line,
        width: 600,
        height: 400
      };
      
      console.log(`生成 ${chartType} 图表...`);
      const vis = await render(options);
      const buffer = vis.toBuffer();
      
      res.writeHead(200, { 
        'Content-Type': 'image/png',
        'Content-Length': buffer.length,
        'Content-Disposition': `inline; filename="${chartType}-chart.png"`
      });
      res.end(buffer);
      
    } else if (url.pathname === '/api/health') {
      // 健康检查
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'healthy',
        renderEngine: renderStatus,
        nodeVersion: process.version,
        timestamp: new Date().toISOString()
      }));
      
    } else if (url.pathname === '/api/status') {
      // 详细状态
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        service: 'gpt-vis-ssr-test',
        renderEngine: renderStatus,
        nodeVersion: process.version,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString(),
        supportedCharts: ['line', 'bar', 'pie', 'area', 'scatter']
      }, null, 2));
      
    } else {
      // 404
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('页面未找到');
    }
    
  } catch (error) {
    console.error('请求处理错误:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: error.message }));
  }
});

const PORT = 3002;
server.listen(PORT, () => {
  console.log(`\n🎉 测试服务器启动成功！`);
  console.log(`📍 访问地址: http://localhost:${PORT}`);
  console.log(`🔧 渲染引擎: ${renderStatus === 'real' ? '真实渲染' : '模拟渲染'}`);
  console.log(`\n按 Ctrl+C 停止服务器`);
});

process.on('SIGINT', () => {
  console.log('\n👋 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
