var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/histogram.ts
var histogram_exports = {};
__export(histogram_exports, {
  Histogram: () => Histogram
});
module.exports = __toCommonJS(histogram_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Histogram(options) {
  const {
    data,
    title,
    width = 600,
    height = 400,
    axisYTitle,
    axisXTitle,
    binNumber,
    theme = "default"
  } = options;
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "interval",
    theme: import_theme.THEME_MAP[theme],
    width,
    height,
    data,
    title,
    encode: {
      x: (d) => d,
      y: "count"
    },
    transform: [{ type: "binX", y: "count", thresholds: binNumber }],
    style: { minHeight: 1, columnWidthRatio: 1, inset: 0.5 },
    axis: {
      x: { title: axisXTitle },
      y: { title: axisYTitle }
    },
    animate: false
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Histogram
});
