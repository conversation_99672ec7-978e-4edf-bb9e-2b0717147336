import { CommonOptions } from './types';
type VennDatum = {
    /**
     * Label for the venn chart segment.
     * This should be a string that describes the segment.
     * For example, "A", "B", or "A ∩ B" for intersections.
     * It is used to identify the segment in the chart.
     */
    label?: string;
    /**
     * Value for the venn chart segment.
     * This should be a number that represents the size or count of the segment.
     * For example, if the segment represents a set of items, this could be the number of items in that set.
     * It is used to determine the size of the segment in the chart.
     * For intersections, this value should represent the count of items that belong to the intersection of sets.
     * For example, if "A ∩ B" represents the intersection of sets A and B, this value should be the count of items that are in both sets.
     */
    value: number;
    /**
     * Sets that the segment belongs to.
     * This should be an array of strings representing the sets that the segment is part of.
     * For example, if the segment represents items that belong to both set A and set B, this could be ['A', 'B'].
     * It is used to determine how the segment is displayed in the venn chart.
     * For intersections, this array should include the sets that contribute to the intersection.
     * For example, if "A ∩ B" represents the intersection of sets A and B, this array should be ['A', 'B'].
     */
    sets: string[] | number[];
};
export type VennOptions = CommonOptions & {
    /**
     * Title of the venn chart.
     */
    title?: string;
    /**
     * The percentage value to display in the liquid chart.
     * This should be a number between 0 and 1, where 1 represents 100%.
     * For example, 0.75 represents 75%.
     */
    data: VennDatum[];
};
export declare function Venn(options: VennOptions): Promise<import("@antv/g2-ssr").Chart>;
export {};
