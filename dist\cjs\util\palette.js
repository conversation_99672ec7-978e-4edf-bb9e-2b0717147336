var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/util/palette.ts
var palette_exports = {};
__export(palette_exports, {
  ACADEMY_COLOR_PALETTE: () => ACADEMY_COLOR_PALETTE,
  DEFAULT_COLOR_PALETTE: () => DEFAULT_COLOR_PALETTE
});
module.exports = __toCommonJS(palette_exports);
var DEFAULT_COLOR_PALETTE = [
  "#1783FF",
  "#F08F56",
  "#D580FF",
  "#00C9C9",
  "#7863FF",
  "#DB9D0D",
  "#60C42D",
  "#FF80CA",
  "#2491B3",
  "#17C76F"
];
var ACADEMY_COLOR_PALETTE = [
  "#4e79a7",
  "#f28e2c",
  "#e15759",
  "#76b7b2",
  "#59a14f",
  "#edc949",
  "#af7aa1",
  "#ff9da7",
  "#9c755f",
  "#bab0ab"
];
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ACADEMY_COLOR_PALETTE,
  DEFAULT_COLOR_PALETTE
});
