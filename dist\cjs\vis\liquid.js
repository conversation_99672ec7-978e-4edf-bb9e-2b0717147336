var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/vis/liquid.ts
var liquid_exports = {};
__export(liquid_exports, {
  Liquid: () => Liquid
});
module.exports = __toCommonJS(liquid_exports);
var import_g2_ssr = require("@antv/g2-ssr");
var import_theme = require("../theme");
async function Liquid(options) {
  const {
    percent,
    title,
    width = 600,
    height = 400,
    theme = "default",
    shape = "circle"
  } = options;
  const inferFontSize = Math.min(width, height) / 10;
  const fontSize = Math.min(Math.max(inferFontSize, 24), 64);
  return await (0, import_g2_ssr.createChart)({
    devicePixelRatio: 3,
    type: "liquid",
    theme: import_theme.THEME_MAP[theme],
    title,
    width,
    height,
    data: percent,
    style: {
      shape,
      contentFontSize: fontSize,
      contentFill: "#000",
      contentStroke: "#fff",
      contentLineWidth: 2,
      outlineBorder: 4,
      outlineDistance: 4,
      waveLength: 128
    },
    animate: false
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Liquid
});
