import { G6 } from '@antv/g6-ssr';
import { type MindMapProps } from '@antv/gpt-vis/dist/esm/MindMap';
import { CommonOptions } from './types';
export type MindMapOptions = CommonOptions & MindMapProps;
/**
 * Converts a tree structure to a graph data format suitable for G6 visualization.
 * The function transforms each node in the tree to a graph node and each parent-child relationship to a graph edge.
 * @param data
 * @returns
 */
export declare function treeToGraphData(data: any): G6.GraphData;
export declare function MindMap(options: MindMapOptions): Promise<import("@antv/g6-ssr").Graph>;
