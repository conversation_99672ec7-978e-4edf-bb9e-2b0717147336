# GPT-Vis SSR 本地部署指南

## 项目简介

GPT-Vis SSR 是 AntV GPT-Vis 项目的服务器端渲染子模块，主要用于在 Node.js 环境中生成各种类型的图表图片。

## 系统要求

- **Node.js**: >= 18.0.0
- **操作系统**: Windows, macOS, Linux
- **内存**: 建议 >= 2GB
- **磁盘空间**: >= 500MB

## 安装步骤

### 1. 克隆项目

```bash
git clone https://github.com/antvis/GPT-Vis.git
cd gpt-vis-ssr
```

### 2. 安装依赖

```bash
npm install
```

**注意**: 如果遇到 canvas 模块编译问题，可能需要：

```bash
# Windows 用户可能需要安装构建工具
npm install --global windows-build-tools

# 重新编译 canvas 模块
npm rebuild canvas
```

### 3. 构建项目

```bash
npm run build
```

### 4. 运行测试

```bash
npm test
```

## 使用方法

### 基本用法

```javascript
const { render } = require('@antv/gpt-vis-ssr');

async function generateChart() {
  const options = {
    type: 'line',
    data: [
      { time: 2018, value: 91.9 },
      { time: 2019, value: 99.1 },
      { time: 2020, value: 101.6 },
      { time: 2021, value: 114.4 },
      { time: 2022, value: 121 },
    ],
    width: 600,
    height: 400,
    axisXTitle: 'Year',
    axisYTitle: 'Value'
  };

  const vis = await render(options);
  
  // 获取图片 Buffer
  const buffer = vis.toBuffer();
  
  // 保存到文件
  vis.exportToFile('./output/chart.png');
  
  return buffer;
}
```

### 支持的图表类型

| 类型 | 说明 | 示例用途 |
|------|------|----------|
| `line` | 线图 | 趋势分析 |
| `bar` | 柱状图 | 分类对比 |
| `column` | 列图 | 数据对比 |
| `area` | 面积图 | 数量变化 |
| `pie` | 饼图 | 占比分析 |
| `scatter` | 散点图 | 相关性分析 |
| `radar` | 雷达图 | 多维度评估 |
| `boxplot` | 箱线图 | 统计分析 |
| `histogram` | 直方图 | 分布分析 |
| `violin` | 小提琴图 | 密度分布 |
| `funnel` | 漏斗图 | 转化分析 |
| `sankey` | 桑基图 | 流向分析 |
| `treemap` | 树图 | 层级结构 |
| `liquid` | 水波图 | 进度展示 |
| `word-cloud` | 词云 | 文本分析 |
| `network-graph` | 网络图 | 关系网络 |
| `flow-diagram` | 流程图 | 流程展示 |
| `mind-map` | 思维导图 | 知识结构 |
| `organization-chart` | 组织架构图 | 组织结构 |
| `fishbone-diagram` | 鱼骨图 | 因果分析 |
| `dual-axes` | 双轴图 | 双指标对比 |
| `venn` | 韦恩图 | 集合关系 |

## 部署方案

### 方案一：直接使用

```javascript
// 在您的 Node.js 项目中直接使用
const { render } = require('@antv/gpt-vis-ssr');

// 在 Express 应用中使用
app.post('/generate-chart', async (req, res) => {
  try {
    const vis = await render(req.body);
    const buffer = vis.toBuffer();
    
    res.set('Content-Type', 'image/png');
    res.send(buffer);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

### 方案二：HTTP 服务

我们提供了一个示例 HTTP 服务器 (`server.js`)：

```bash
# 启动服务器
node server.js

# 访问地址
http://localhost:3000
```

### 方案三：Docker 部署

创建 `Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["node", "server.js"]
```

构建和运行：

```bash
docker build -t gpt-vis-ssr .
docker run -p 3000:3000 gpt-vis-ssr
```

## 常见问题

### 1. Canvas 模块编译失败

**问题**: `Error: The module was compiled against a different Node.js version`

**解决方案**:
```bash
# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install

# 或者重新编译 canvas
npm rebuild canvas
```

### 2. 内存不足

**问题**: 生成大量图表时内存溢出

**解决方案**:
```bash
# 增加 Node.js 内存限制
node --max-old-space-size=4096 your-app.js
```

### 3. 字体问题

**问题**: 中文字符显示异常

**解决方案**: 确保系统安装了中文字体，或在代码中指定字体：

```javascript
const options = {
  type: 'line',
  data: [...],
  theme: {
    fontFamily: 'SimHei, Arial, sans-serif'
  }
};
```

## 性能优化

1. **缓存机制**: 对相同配置的图表进行缓存
2. **批量处理**: 使用队列处理大量图表生成请求
3. **资源限制**: 限制并发生成数量
4. **内存管理**: 及时释放不需要的资源

## 监控和日志

建议添加以下监控：

- 图表生成成功率
- 平均生成时间
- 内存使用情况
- 错误日志记录

## 技术支持

- **项目主页**: https://gpt-vis.antv.vision
- **GitHub**: https://github.com/antvis/GPT-Vis
- **文档**: https://gpt-vis.antv.vision/docs
- **问题反馈**: https://github.com/antvis/GPT-Vis/issues

## 许可证

MIT License - 详见 [LICENSE](./LICENSE) 文件
