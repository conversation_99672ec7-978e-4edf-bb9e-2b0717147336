import { G6 } from '@antv/g6-ssr';
/**
 * Converts a tree structure to a graph data format suitable for G6 visualization.
 * The function transforms each node in the tree to a graph node and each parent-child relationship to a graph edge.
 * @param data
 *
 * type TreeGraphData = {
 *   name: string;
 *   children?: TreeGraphData[];
 *   [key: string]: any;
 * };
 * @returns
 */
export declare function treeToGraphData(data: any): G6.GraphData;
