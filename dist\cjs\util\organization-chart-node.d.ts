import { G6 } from '@antv/g6-ssr';
declare const Rect: typeof G6.Rect;
/**
 * Custom node for OrganizationChart visualization.
 */
export declare class OrganizationChartNode extends Rect {
    get data(): any;
    getLabelStyle(): any;
    getKeyStyle(attributes: any): any;
    drawDescriptionShape(attributes: any, container: any): void;
    drawOrganizationIconShape(attributes: any, container: any): void;
    render(attributes?: Required<G6.RectStyleProps>, container?: this): void;
}
export {};
