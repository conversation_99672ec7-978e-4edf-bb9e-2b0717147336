import { CommonOptions } from './types';
type SankeyDatum = {
    source: string;
    target: string;
    value: number;
};
export type SankeyOptions = CommonOptions & {
    /**
     * Title of the sankey chart.
     */
    title?: string;
    /**
     * The Data for the sankey chart.
     * Each datum should have a source, target, and value.
     * The source and target are strings representing the nodes,
     * and value is a number representing the weight of the link.
     * For example:
     * [
     *   { source: 'A', target: 'B', value: 10 },
     *   { source: 'B', target: 'C', value: 5 },
     *   { source: 'A', target: 'C', value: 15 },
     * ]
     */
    data: SankeyDatum[];
    /**
     * Node alignment for the sankey chart.
     * Options are 'left', 'center', 'right', or 'justify'.
     * - 'left': Aligns nodes to the left.
     * - 'center': Centers nodes in the middle.
     * - 'right': Aligns nodes to the right.
     * - 'justify': Justifies nodes across the width of the chart.
     * Default is 'center'.
     */
    nodeAlign?: 'left' | 'center' | 'right' | 'justify';
};
export declare function Sankey(options: SankeyOptions): Promise<import("@antv/g2-ssr").Chart>;
export {};
